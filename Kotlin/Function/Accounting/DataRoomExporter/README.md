### To run locally
```shell
GOOGLE_APPLICATION_CREDENTIALS=/path/to/credentials/.config/gcloud/service-account.json \
SERVICE_NAME=data-room-exporter \
HOSTNAME=local.herohero.co \ 
HOSTNAME_SERVICES=https://svc-devel.herohero.co \
CLOUD_PROJECT=heroheroco \
ENVIRONMENT=prod \
PRODUCTION=true \
SERVICE_TYPE=cloud_function \
CLOUD_REGION=europe gradle \
runFunction \
-Prun.functionTarget=hero.functions.DataRoomExporter
```
### Setup 
Once in 90 days, `hostSessionKey` needs to be refreshed, this key is stored in Firestore in `constants/prod-stripe`.
This key is a value of cookie `__Host-session`, that is set when you log in to https://dashboard.stripe.com and is sent with every request.
For more info about this cookie, see https://stripe.com/en-cz/cookie-settings, it's among `Essential` cookies.

### Manual downloads
Go to [Stripe revenue reports](https://dashboard.stripe.com/billing/revenue) and download `Subscription metrics per month`
in section `Report Downloads` and upload these to
[Google drive data room folder](https://drive.google.com/drive/u/1/folders/1UM4Ble9CyUKdcw0uOlUeqKZHY2xVdr28). 

### Deploy
The function is deployed only on devel here:

https://europe-west1-heroheroco.cloudfunctions.net/devel-data-room-export

### Usage
You can use query parameter `financialsCurrency` to change the currency in financial(flexibee) export.
For example:

https://europe-west1-heroheroco.cloudfunctions.net/devel-data-room-export?financialsCurrency=EUR
