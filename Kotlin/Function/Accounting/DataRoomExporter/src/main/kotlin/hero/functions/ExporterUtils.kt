package hero.functions

import java.time.Year
import java.time.YearMonth

internal val monthsRange = 1..12

internal fun yearMonthsRange(since: YearMonth): List<YearMonth> {
    var current = since
    val end = YearMonth.now().plusMonths(12)
    val yearMonths = mutableListOf<YearMonth>()
    while (current.isBefore(end)) {
        yearMonths.add(current)
        current = current.plusMonths(1)
    }

    return yearMonths
}

internal fun yearRange(since: Year) = ((since.value)..Year.now().value).map { Year.of(it) }
