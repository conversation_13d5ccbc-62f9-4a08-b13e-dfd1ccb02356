package hero.functions

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.kittinunf.fuel.core.Headers
import com.github.kittinunf.fuel.httpGet
import hero.baseutils.fetch
import hero.baseutils.log
import hero.gcloud.CellHorizontalAlignment
import hero.gcloud.Color
import hero.gcloud.Format
import hero.gcloud.FormattedValue
import hero.gcloud.NumberBetweenBooleanFormatRule
import hero.gcloud.PERCENT_PATTERN
import hero.gcloud.Range
import hero.gcloud.SheetsCurrency
import hero.gcloud.SheetsRow
import hero.gcloud.SpreadsheetRef
import hero.gcloud.addConditionalFormatRules
import hero.gcloud.addSheet
import hero.gcloud.appendFormatted
import hero.gcloud.batched
import hero.gcloud.currency
import hero.gcloud.emptyRow
import hero.gcloud.number
import hero.gcloud.resizeColumns
import hero.gcloud.string
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter

internal class StripeCohortsExporter(private val stripeHostSessionKey: String) : DataExporter {
    override fun invoke(spreadsheet: SpreadsheetRef) {
        val sheetId = try {
            spreadsheet.addSheet("Stripe cohorts")
        } catch (e: Exception) {
            log.fatal("Failed to create new sheet", cause = e)
            throw e
        }

        spreadsheet
            .batched()
            .appendFormatted(
                cohortByRevenue() + listOf(emptyRow) + cohortByCustomer(),
                sheetId = sheetId,
            )
            .addConditionalFormatRules(
                Range(),
                stripeCohortColorRules,
                sheetId = sheetId,
            )
            .resizeColumns(1, 2, 125, sheetId = sheetId)
            .resizeColumns(3, 12, 65, sheetId = sheetId)
            .execute()
    }

    private fun cohortByRevenue(): List<SheetsRow> {
        val cohorts = fetchCohortData(StripeCohortTypes.BY_REVENUE)
        val data = cohorts.map {
            it.render { startingValue ->
                currency(startingValue / 100.0, SheetsCurrency.EUR)
            }
        }

        return listOf(listOf(string("By revenue", Format(bold = true))), cohortTableHeader("Start MRR")) + data
    }

    private fun cohortByCustomer(): List<SheetsRow> {
        val cohorts = fetchCohortData(StripeCohortTypes.BY_CUSTOMER)
        val data = cohorts.map {
            it.render { startingValue ->
                number(startingValue, pattern = "#")
            }
        }

        return listOf(listOf(string("By subscribers", Format(bold = true))), cohortTableHeader("Start value")) + data
    }

    private fun fetchCohortData(
        type: StripeCohortTypes,
        endDate: LocalDate = LocalDate.now(),
    ): List<CohortResponse> {
        val zoneOffset = ZoneId.of("Europe/Prague").rules.getOffset(Instant.now())
        val endTime = LocalDateTime.of(endDate, LocalTime.MAX).toEpochSecond(zoneOffset)
        try {
            return "https://dashboard.stripe.com/ajax/billing_overview/${type.path}"
                .httpGet(listOf("currency" to "eur", "end_time" to endTime))
                .header(
                    Headers.COOKIE to "__Host-session=$stripeHostSessionKey;",
                    "stripe-livemode" to "true",
                ).fetch<CohortsListResponse>()
                .cohorts
        } catch (e: Exception) {
            log.fatal("Failed to fetch cohort data from Stripe, try refreshing hostSessionKey", cause = e)
            throw e
        }
    }
}

private fun CohortResponse.render(startingValueMapper: (Double) -> FormattedValue): SheetsRow {
    val firstColumn = if (cohort == "*") {
        "Average"
    } else {
        YearMonth.parse(cohort, inputDateFormatter).format(outputDateFormatter)
    }

    return listOf(
        string(firstColumn),
        startingValueMapper(startingValue),
    ) + data.map { number(it.value.toDouble() / it.cohortSize.toDouble(), pattern = PERCENT_PATTERN) }
}

private val inputDateFormatter = DateTimeFormatter.ofPattern("yyyy-MM")
private val outputDateFormatter = DateTimeFormatter.ofPattern("MMMM yyyy")

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class CohortsListResponse(val `object`: String, val cohorts: List<CohortResponse>)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class CohortResponse(val cohort: String, val startingValue: Double, val data: List<CohortData>)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
private data class CohortData(val month: Int, val value: Int, val cohortSize: Int)

private val stripeCohortColorRules = listOf(
    // RGB (98, 90, 250)
    NumberBetweenBooleanFormatRule(0.80, 1.0, Format(backgroundColor = Color(0.384, 0.353, 0.980))),
    // RGB (141, 127, 250)
    NumberBetweenBooleanFormatRule(0.66, 0.80, Format(backgroundColor = Color(0.553, 0.498, 0.980))),
    // RGB (180, 156, 252)
    NumberBetweenBooleanFormatRule(0.53, 0.66, Format(backgroundColor = Color(0.706, 0.612, 0.988))),
    // RGB (209, 190, 254)
    NumberBetweenBooleanFormatRule(0.42, 0.53, Format(backgroundColor = Color(0.8196, 0.7451, 0.9961))),
    // RGB (242, 235, 255)
    NumberBetweenBooleanFormatRule(0.0, 0.42, Format(backgroundColor = Color(0.949, 0.922, 1))),
)

private enum class StripeCohortTypes(val path: String) {
    BY_REVENUE("cohort_churn_by_revenue"),
    BY_CUSTOMER("cohort_customer_churn"),
}

private fun cohortTableHeader(startValueColumnName: String) =
    listOf(
        string("Subscribed", format = Format(horizontalAlignment = CellHorizontalAlignment.LEFT, bold = true)),
        string(startValueColumnName, format = Format(horizontalAlignment = CellHorizontalAlignment.RIGHT, bold = true)),
    ) + (1..12).map {
        string(
            "Month $it",
            format = Format(horizontalAlignment = CellHorizontalAlignment.RIGHT, bold = true),
        )
    }
