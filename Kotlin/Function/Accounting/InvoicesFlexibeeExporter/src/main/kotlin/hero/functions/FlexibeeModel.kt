package hero.functions

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

data class InvoiceData(
    val winstrom: WinStromData,
)

data class InvoiceVariableData(
    val clenKonVykDph: String,
    val popis: String,
    val clenDph: String,
    val typUcOp: String,
    val typSzbDphK: String,
)

data class WinStromData(
    @JsonProperty("@version")
    val version: String = "1.0",
    @JsonProperty("adresar")
    val adresar: List<Adresar>?,
    @JsonProperty("faktura-vydana")
    val fakturaVydana: List<FakturaVydanaData>?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class Adresar(
    @JsonProperty("@update")
    val update: String = "ok",
    val id: String?,
    val nazev: String?,
    val ulice: String?,
    val mesto: String?,
    val psc: String?,
    val mobil: String?,
    val email: String?,
    val stat: String,
    val ic: String?,
    val dic: String?,
    val platceDph: Boolean,
    val typVztahuK: String = "typVztahu.odberDodav",
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class FakturaVydanaData(
    // use @update=ok only for manual updates
    @JsonProperty("@update")
    val update: String = "ignore",
    val id: String?,
    val varSym: String?,
    val konSym: String?,
    val poznam: String?,
    val bankovniUcet: String?,
    val typDoklBan: String?,
    val typUcOp: String?,
    val statDph: String?,
    // we cannot use LocalDate, because Flexibee returns non standard yyyy-MM-ddXXX
    // and it cannot be simply ignored with @JsonFormat(shape = STRING, pattern = "yyyy-MM-dd")
    val datVyst: String?,
    val duzpPuv: String?,
    val datSplat: String?,
    val datUhr: String?,
    val datUcto: String?,
    val firma: String?,
    val popis: String?,
    val zaokrNaSumK: String? = "zaokrNa.zadne",
    val mena: String,
    val typDokl: String?,
    val stavUhrK: String?,
    val polozkyFaktury: List<PolozkaFakturyData>?,
    val clenKonVykDph: String?,
    val clenDph: String?,
    // used only for reading
    val kod: String? = null,
    val sumCelkem: BigDecimal? = null,
    val sumCelkemMen: BigDecimal? = null,
    val sumZklZaklMen: BigDecimal? = null,
    val sumDphZaklMen: BigDecimal? = null,
    // we use `kurz` only to re-fetch conversion rate set by Flexibee
    val kurz: String? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class PolozkaFakturyData(
    @JsonProperty("@update")
    val update: String = "ok",
    val id: String,
    val nazev: String,
    val mnozMj: BigDecimal,
    val cenaMj: BigDecimal?,
    val sumZklMen: BigDecimal?,
    val sumDphMen: BigDecimal?,
    val sumCelkemMen: BigDecimal?,
    val typeCenyDphK: String = "typCeny.bezDph",
    val typSzbDphK: String?,
    val szbDph: BigDecimal?,
    val mena: String?,
    val clenKonVykDph: String?,
    val clenDph: String?,
    val kopClenDph: Boolean = false,
    val kopClenKonVykDph: Boolean = false,
)
