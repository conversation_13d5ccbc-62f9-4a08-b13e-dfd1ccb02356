package hero.functions

import hero.baseutils.SystemEnv
import hero.gcloud.PubSub
import hero.model.topics.DiscordMemberChanged
import hero.model.topics.SubscriberStatusChanged

@Suppress("Unused")
class DiscordSubscriptionHandler(
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
) : PubSubSubscriber<SubscriberStatusChanged>() {
    override fun consume(payload: SubscriberStatusChanged) {
        // We just republish to topic relevant for Discord. This is necessary so that we can publish
        // to the same topic form DiscordConnectionHandler.
        pubSub.publish(
            DiscordMemberChanged(
                userId = payload.userId,
                creatorId = payload.creatorId,
                statusChange = payload.statusChange,
            ),
        )
    }
}
