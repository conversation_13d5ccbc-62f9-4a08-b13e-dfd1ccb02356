package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.systemEnvRelaxed
import hero.gcloud.PubSub
import hero.gcloud.contains
import hero.gcloud.firestore
import hero.gcloud.increment
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.gjirafa.GjirafaLivestreamsService
import hero.gjirafa.GjirafaUploadsService
import hero.model.Post
import hero.model.PostCounts
import hero.model.SavedPost
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.jooq.DSLContext
import java.time.Instant

@Suppress("unused")
class DeletedPostHandler(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
) : PubSubSubscriber<PostStateChanged>() {
    private val context by lazyContext
    private val firestore = firestore(SystemEnv.cloudProject, SystemEnv.isProduction)
    private val postsCollection = firestore.typedCollectionOf(Post)
    private val savedPostsCollection = firestore.typedCollectionOf(SavedPost)
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject)
    private val gjirafa: GjirafaUploadsService = GjirafaUploadsService(
        projectId = SystemEnv.gjirafaProject,
        apiKey = SystemEnv.gjirafaApiKey,
        imageKey = SystemEnv.gjirafaImageKey,
    )
    private val livestreams: GjirafaLivestreamsService = GjirafaLivestreamsService(
        projectId = SystemEnv.gjirafaProject,
        apiKey = SystemEnv.gjirafaApiKey,
    )

    override fun consume(payload: PostStateChanged) {
        val post = payload.post
        if (payload.stateChange != PostStateChange.DELETED) {
            log.info("Ignoring non-deleted post ${post.id}.")
            return
        }

        log.info("Handling deleted post ${post.id}.", mapOf("userId" to post.userId, "postId" to post.id))
        post.parentId?.let { parentId -> decrementParentCounts(parentId) }

        // deleting of all child posts
        postsCollection
            .where(Post::parentId).isEqualTo(post.id)
            .and(Post::state).isIn(listOf(PostState.PROCESSING, PostState.PUBLISHED, PostState.SCHEDULED))
            .fetchAll()
            .forEach { child ->
                log.info("Deleting child post ${child.id}.", mapOf("userId" to child.userId, "postId" to child.id))
                postsCollection[child.id].field(Post::state).update(PostState.DELETED)
                pubSub.publish(PostStateChanged(PostStateChange.DELETED, child))
            }

        runBlocking(Dispatchers.IO) {
            // deleting of all related notifications
            context
                .update(Tables.NOTIFICATION)
                .set(Tables.NOTIFICATION.DELETED_AT, Instant.now())
                .where(Tables.NOTIFICATION.OBJECT_POST_ID.eq(post.id))
                .execute()

            // deleting of all saved posts
            val savedPostsDeletion = savedPostsCollection
                .where(SavedPost::postId).isEqualTo(post.id)
                .fetchAll()
                .map {
                    async { savedPostsCollection[it.id].delete() }
                }

            val gjirafaAssetsDeletion = post.assets
                .mapNotNull { it.gjirafa?.id }
                .map {
                    async { deleteUnusedGjirafaAsset(it) }
                }

            val gjirafaLivestreamsDeletion = post.assets
                .mapNotNull { it.gjirafaLive?.id }
                .map {
                    async { deleteUnusedGjirafaLivestream(it) }
                }

            savedPostsDeletion.awaitAll()
            gjirafaAssetsDeletion.awaitAll()
            gjirafaLivestreamsDeletion.awaitAll()
        }
    }

    private fun deleteUnusedGjirafaAsset(assetId: String) {
        // delete asset ONLY if the media is not assigned to some other post at the same time
        val posts = postsCollection.where(Post::assetIds).contains(assetId).fetchAll()
        if (posts.none { it.state == PostState.PUBLISHED }) {
            gjirafa.delete(assetId)
        }
    }

    private fun deleteUnusedGjirafaLivestream(assetId: String) {
        if (systemEnvRelaxed("FF_DELETE_LIVE_VIDEOS") != "disabled") {
            // delete asset ONLY if the media is not assigned to some other post at the same time
            val posts = postsCollection.where(Post::assetIds).contains(assetId).fetchAll()
            if (posts.none { it.state == PostState.PUBLISHED }) {
                livestreams.deleteLiveVideo(assetId)
            }
        }
    }

    private fun decrementParentCounts(postId: String) {
        postsCollection[postId].field(root(Post::counts).path(PostCounts::comments)).increment(-1)
        getPost(postId).let { parent ->
            val parentId = parent.parentId ?: return
            postsCollection[parentId].field(root(Post::counts).path(PostCounts::replies)).increment(-1)
        }
    }

    private fun getPost(id: String): Post = postsCollection[id].get()
}
