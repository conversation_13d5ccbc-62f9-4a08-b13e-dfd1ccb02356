package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.gcloud.PubSub
import hero.jackson.toJson
import hero.model.SlackBlock
import hero.model.SlackBlockText
import hero.model.SlackMessage
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged

@Suppress("Unused")
class PostSlackAlerter : PubSubSubscriber<PostStateChanged>() {
    private val production = SystemEnv.isProduction
    private val hostname = SystemEnv.hostname
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject)

    override fun consume(payload: PostStateChanged) {
        val post = payload.post
        // TODO handle only PUBLISHED
        if (payload.stateChange !in setOf(PostStateChange.PUBLISHED)) {
            log.debug("State change for ${post.id} is not PUBLISHED, skipping: ${payload.stateChange}.")
            return
        }
        if (post.parentId != null) {
            log.debug("Not notifying Slack for comment ${post.id} of post ${post.parentId}.")
            return
        }
        if (post.messageThreadId != null) {
            log.debug("Not notifying Slack for DM ${post.id}.")
            return
        }
        val message = SlackMessage(
            channel = "alerts_posts_${production.envPrefix}",
            blocks = listOf(
                SlackBlock(
                    SlackBlockText(
                        "*<$hostname/${post.userId}/|${post.userId}>* created a post." +
                            "\n\n" +
                            // prefix lines with > to blockquote
                            post.text.split("\n").map { "> $it" }.joinToString("\n"),
                    ),
                ),
            ),
        )
        log.debug("Publishing Slack notification for ${post.id}.", mapOf("payload" to message.toJson()))
        pubSub.publish(message)
    }
}
