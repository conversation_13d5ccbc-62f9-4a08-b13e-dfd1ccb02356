package hero.functions

import com.google.cloud.RetryOption
import com.google.cloud.bigquery.BigQuery
import com.google.cloud.bigquery.BigQueryOptions
import com.google.cloud.bigquery.JobId
import com.google.cloud.bigquery.JobInfo
import com.google.cloud.bigquery.QueryJobConfiguration
import com.google.cloud.bigquery.TableResult
import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.core.logging.Logger
import hero.model.topics.Daily
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.tables.DailyPostViewStatistics.DAILY_POST_VIEW_STATISTICS
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.threeten.bp.Duration
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneOffset
import java.util.UUID

@Suppress("unused")
class PostViewStatisticsWriter(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val bigQuery: BigQuery = BigQueryOptions.getDefaultInstance().service,
    private val log: Logger = hero.baseutils.log,
    private val systemEnv: EnvironmentVariables = SystemEnv,
) : PubSubSubscriber<Daily>(systemEnv) {
    private val postViewsUpdater = PostViewsUpdater(lazyContext)

    // this must be lazy otherwise function deployment fails when the function is first instantiated
    private val context: DSLContext by lazyContext

    override fun consume(payload: Daily) {
        // we always process day before
        processSingleDay(LocalDate.now().minusDays(1))
    }

    internal fun processSingleDay(date: LocalDate) {
        log.info("Processing post view statistics for day $date")
        val startOfDay = date.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli()
        val endOfDay = date.atTime(LocalTime.MAX).toInstant(ZoneOffset.UTC).toEpochMilli()

        val query = """
        SELECT
          COUNT(*) AS views,
          trim(event_params.post_id) as post_id,
          trim(event_params.item_id) as creator_id
        FROM
          heroheroco.${systemEnv.isProduction.envPrefix}.events_data
        WHERE
          event_name = 'view_post'
          AND event_timestamp > $startOfDay
          AND event_timestamp < $endOfDay
        GROUP BY
          trim(event_params.post_id),
          trim(event_params.item_id)
        ORDER BY
          views DESC;
        """.trimIndent()

        val result = executeQuery(query)

        log.info("Finished processing post view statistics for day $date")
        val stats = result
            ?.iterateAll()
            ?.map {
                DSL.row(
                    date,
                    it["post_id"].stringValue.trim(),
                    it["creator_id"].getStringValueOrDefault("null-value").trim(),
                    it["views"].longValue.toInt(),
                )
            }
            ?: emptyList()
        if (stats.isEmpty()) {
            log.warn("Found zero stats for day $date")
            return
        }

        context
            .insertInto(
                DAILY_POST_VIEW_STATISTICS,
                DAILY_POST_VIEW_STATISTICS.DATE,
                DAILY_POST_VIEW_STATISTICS.POST_ID,
                DAILY_POST_VIEW_STATISTICS.CREATOR_ID,
                DAILY_POST_VIEW_STATISTICS.VIEWS,
            )
            .valuesOfRows(stats)
            .execute()

        postViewsUpdater.execute(UpdatePostViews(date))
    }

    private fun executeQuery(query: String): TableResult? {
        val config = QueryJobConfiguration
            .newBuilder(query)
            .setUseLegacySql(false)
            .build()

        val jobId = JobId.of(UUID.randomUUID().toString())
        val queryJob = bigQuery
            .create(JobInfo.newBuilder(config).setJobId(jobId).build())
            .waitFor(RetryOption.totalTimeout(Duration.ofSeconds(20)))

        if (queryJob == null) {
            error("Job $jobId no longer exists")
        } else if (queryJob.status.error != null) {
            error(queryJob.status.error.toString())
        }

        return queryJob.getQueryResults()
    }
}
