package hero.gjirafa

import com.github.kittinunf.fuel.core.Request

internal fun Request.authorize(
    apiKey: String,
    contentType: String? = "application/json",
): Request =
    this
        .header("Api-Key", apiKey)
        .let {
            if (contentType != null) {
                it.header("Content-Type", contentType)
            } else {
                it
            }
        }

internal const val VIDEO_BASE_URL = "https://vp-api.gjirafa.tech/api/projects"
internal const val AUDIO_BASE_URL = "https://audio.vp.gjirafa.tech/api/v1"
