package hero.gjirafa

import com.github.kittinunf.fuel.core.BlobDataPart
import com.github.kittinunf.fuel.core.Method
import com.github.kittinunf.fuel.httpDelete
import com.github.kittinunf.fuel.httpGet
import com.github.kittinunf.fuel.httpPost
import com.github.kittinunf.fuel.httpPut
import com.github.kittinunf.fuel.httpUpload
import hero.baseutils.FuelException
import hero.baseutils.fetch
import hero.baseutils.log
import hero.baseutils.truncate
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.GoneException
import hero.exceptions.http.HttpStatusException
import hero.gjirafa.dto.CutLivestreamRequest
import hero.gjirafa.dto.CutLivestreamResponse
import hero.gjirafa.dto.GjirafaChannelResponse
import hero.gjirafa.dto.GjirafaInputType
import hero.gjirafa.dto.GjirafaLatencyType
import hero.gjirafa.dto.GjirafaStreamType
import hero.gjirafa.dto.LiveVideoResponseV1
import hero.gjirafa.dto.LiveVideoResponseV2
import hero.gjirafa.dto.PostLiveVideoRequest
import hero.gjirafa.dto.PostLiveVideoThumbnail
import hero.gjirafa.dto.PutLiveVideoRequest
import hero.gjirafa.dto.StopAndCutRequest
import hero.jackson.toJson
import hero.model.GjirafaResponse

class GjirafaLivestreamsService(
    private val projectId: String,
    private val apiKey: String,
) {
    // https://vp.gjirafa.tech/documentation/api/livestream-api/channels/API%20V1/createNewChannel
    // https://livestream.vp.gjirafa.tech/swagger/index.html?urls.primaryName=VP%20Livestream%20API%20V1#/LiveVideo/post_api_v1__projectId__live_videos
    fun postChannel(
        userId: String,
        name: String,
        imageUrl: String,
    ): GjirafaChannelResponse {
        val logoResponse = imageUrl.httpGet().response().second
        val logoBytes = logoResponse.data
        val contentType = logoResponse.header("Content-Type").first()
        val suffix = contentType.substringAfterLast('/', "")
        val body = listOf(
            // title must not exceed 50 chars
            "Title" to name.truncate(50),
            "Type" to GjirafaStreamType.STANDARD.value,
            "LatencyType" to GjirafaLatencyType.LOW.value,
            "InputType" to GjirafaInputType.PULL.value,
            "VideoStreamId" to userId,
        )

        return "$LIVESTREAM_URL_V1/$projectId/channels"
            .httpUpload(body, Method.POST)
            .add(
                BlobDataPart(
                    inputStream = logoBytes.inputStream(),
                    name = "Logo",
                    filename = "$userId.$suffix",
                    contentType = contentType,
                ),
            )
            .authorize(apiKey, null)
            .fetch<GjirafaResponse<GjirafaChannelResponse>>()
            .result
    }

    // https://vp.gjirafa.tech/documentation/api/livestream-api/channels/API%20V1/getSingleChannelDetailed
    fun getChannel(channelId: String): GjirafaChannelResponse =
        "$LIVESTREAM_URL_V1/$projectId/channels/$channelId"
            .httpGet()
            .authorize(apiKey, null)
            .fetch<GjirafaResponse<GjirafaChannelResponse>>()
            .result

    // https://vp.gjirafa.tech/documentation/api/livestream-api/channels/API%20V1/deleteLiveChannel
    fun deleteChannel(channelId: String): Boolean =
        "$LIVESTREAM_URL_V1/$projectId/channels/$channelId"
            .httpDelete()
            .authorize(apiKey, null)
            .fetch<GjirafaResponse<Boolean>>()
            .result

    // https://vp.gjirafa.tech/documentation/api/livestream-api/liveVideo/API%20V1/createLiveVideo
    fun postLiveVideo(body: PostLiveVideoRequest): LiveVideoResponseV1 =
        "$LIVESTREAM_URL_V1/$projectId/live-videos"
            .httpPost()
            .body(body.toJson())
            .authorize(apiKey)
            .fetch<GjirafaResponse<LiveVideoResponseV1>>()
            .result

    // https://vp.gjirafa.tech/documentation/api/livestream-api/liveVideo/API%20V2/updateExistingLiveVideo
    fun putLiveVideo(
        id: String,
        body: PutLiveVideoRequest,
    ): LiveVideoResponseV2 =
        "$LIVESTREAM_URL_V2/$projectId/live-videos/$id"
            .httpPut()
            .body(body.toJson())
            .authorize(apiKey)
            .fetch<GjirafaResponse<LiveVideoResponseV2>>()
            .result

    // https://vp.gjirafa.tech/documentation/api/livestream-api/liveVideo/API%20V1/getLiveVideoDetails
    fun getLiveVideo(id: String): LiveVideoResponseV2 =
        try {
            "$LIVESTREAM_URL_V2/$projectId/live-videos/$id"
                .httpGet()
                .authorize(apiKey)
                .fetch<GjirafaResponse<LiveVideoResponseV2>>()
                .result
        } catch (e: FuelException) {
            if (e.status == 410) {
                throw GoneException("Live video $id has been stopped and converted to regular video.")
            }
            throw HttpStatusException(
                e.status,
                "Couldn't get live video $id: ${e.message}",
                mapOf("assetId" to id),
                null,
            )
        }

    fun isAuthor(
        userId: String,
        videoId: String,
    ): Boolean {
        val author = getLiveVideo(videoId).author
        return author == userId
    }

    fun assertAuthor(
        userId: String,
        videoId: String,
    ) {
        if (!isAuthor(userId, videoId)) {
            throw ForbiddenException(
                "User $userId is not allowed to access $videoId",
                mapOf(
                    "userId" to userId,
                    "videoId" to videoId,
                ),
            )
        }
    }

    // https://vp.gjirafa.tech/documentation/api/livestream-api/liveVideo/API%20V2/cutAndPublish
    fun cutAndPublish(
        id: String,
        cutRequest: CutLivestreamRequest,
    ): CutLivestreamResponse =
        "$LIVESTREAM_URL_V2/$projectId/live-videos/$id/cut-and-publish"
            .httpPost()
            .authorize(apiKey)
            .body(cutRequest.toJson())
            .fetch<GjirafaResponse<CutLivestreamResponse>>()
            .result

    // https://vp.gjirafa.tech/documentation/api/livestream-api/liveVideo/API%20V2/stopAndCut
    fun stopAndCut(
        id: String,
        cutRequest: StopAndCutRequest,
    ): CutLivestreamResponse =
        "$LIVESTREAM_URL_V2/$projectId/live-videos/$id/stop-and-cut"
            .httpPost()
            .authorize(apiKey)
            .body(cutRequest.toJson())
            .fetch<GjirafaResponse<CutLivestreamResponse>>()
            .result

    // https://vp.gjirafa.tech/documentation/api/livestream-api/liveVideo/API%20V2/stopwithoutsaving
    fun stopWithoutSaving(id: String): Boolean =
        "$LIVESTREAM_URL_V2/$projectId/live-videos/$id/stop"
            .httpPost()
            .authorize(apiKey)
            .fetch<GjirafaResponse<Boolean>>()
            .result

    // https://vp.gjirafa.tech/documentation/api/livestream-api/liveVideo/API%20V2/startStreaming
    fun goLive(id: String): Boolean =
        "$LIVESTREAM_URL_V2/$projectId/live-videos/$id/go-live"
            .httpPut()
            .authorize(apiKey)
            .fetch<GjirafaResponse<Boolean>>()
            .result

    // https://vp.gjirafa.tech/documentation/api/livestream-api/liveVideo/API%20V2/addThumbnail
    fun postVideoThumbnail(
        id: String,
        imageUrl: String,
    ): PostLiveVideoThumbnail {
        val logoResponse = imageUrl.httpGet().response().second
        val logoBytes = logoResponse.data
        val contentType = logoResponse.header("Content-Type").first()
        val suffix = contentType.substringAfterLast('/', "")
        val body = listOf("videoId" to id)
        return "$LIVESTREAM_URL_V2/$projectId/live-videos/$id/thumbnail"
            .httpUpload(body, Method.POST)
            .add(
                BlobDataPart(
                    inputStream = logoBytes.inputStream(),
                    name = "File",
                    filename = "$id.$suffix",
                    contentType = contentType,
                ),
            )
            .authorize(apiKey, null)
            .fetch<GjirafaResponse<PostLiveVideoThumbnail>>()
            .result
    }

    // https://vp.gjirafa.tech/documentation/api/livestream-api/liveVideo/API%20V1/deleteLiveVideo
    fun deleteLiveVideo(assetId: String) {
        try {
            stopWithoutSaving(assetId)
        } catch (e: FuelException) {
            log.warn("Could not stop live-video $assetId: ${e.message}", mapOf("assetId" to assetId))
        }
        try {
            "$LIVESTREAM_URL_V1/$projectId/live-videos/$assetId"
                .httpDelete()
                .authorize(apiKey)
                .fetch<GjirafaResponse<Boolean>>()
                .result
        } catch (e: FuelException) {
            log.warn("Could not delete live-video, maybe already deleted: ${e.message}", mapOf("assetId" to assetId))
        }
    }
}

private const val LIVESTREAM_URL_V1 = "https://livestream.vp.gjirafa.tech/api/v1"
private const val LIVESTREAM_URL_V2 = "https://livestream.vp.gjirafa.tech/api/v2"
