package hero.gjirafa.dto

import hero.baseutils.plusHours
import hero.gjirafa.dto.HealthStatus.UNKNOWN
import hero.gjirafa.dto.LiveVideoStatus.OFFLINE
import hero.model.GjirafaAsset
import hero.model.GjirafaLiveAsset
import hero.model.GjirafaStatus
import hero.model.LiveVideoStatus
import java.time.Instant

val exampleGjirafaAsset = GjirafaAsset(
    id = "vjsnlpex",
    width = 1280,
    height = 720,
    thumbnailBlurhash = "LGSF;JIUofof00RjWBay4nofofj[",
    duration = 1974.138,
    hasVideo = true,
    hasAudio = true,
    progressTillCompleteness = 50,
    progressTillReadiness = 70,
    hidden = false,
    audioByteSize = 268358756,
    status = GjirafaStatus.COMPLETE,
    key = "mdygiendyisjkahakfdn",
    audioStaticUrl = "https://cdn.vpplayer.tech/aaaaaaaa/pLZw5imXMLYIFUFQLCeCag/encode/vjsnlpex/mp3/320kbps.mp3",
    audioStreamUrl = "https://cdn.vpplayer.tech/aaaaaaaa/pLZw5imXMLYIFUFQLCeCag/encode/vjsnlpex/hls/360p/index.m3u8",
    videoStreamUrl = "https://cdn.vpplayer.tech/aaaaaaaa/pLZw5imXMLYIFUFQLCeCag/encode/vjsnlpex/hls/master_file.m3u8",
    chaptersVttUrl = "https://cdn.vpplayer.tech/aaaaaaaa/pLZw5imXMLYIFUFQLCeCag/encode/vjsnlpex/hls/chapter.vtt",
    keyId = "E03AF87C-9F1C-E64C-9BCC-15EC6A3FA352",
    encodingRemainingTime = 600.23,
    encodingFinishTime = Instant.now().plusHours(1),
    createdAt = Instant.now(),
    projectId = "aaaaaaaaa",
    imageKey = "12345789",
)

val exampleGjirafaLiveAsset = GjirafaLiveAsset(
    id = "vjsnlpex",
    playbackUrl = "https://playback-url",
    channelPublicId = "chhdhdhd",
    liveStatus = LiveVideoStatus.LIVE,
)

val exampleGjirafaChannel = GjirafaChannelResponse(
    publicId = "chbddmsbko",
    name = "af1acf0a-300b-4792-83c8-0d854c290419",
    title = "Vojtech Otevrel",
    logo = "https://cdn.vpplayer.tech/aaaaaaaa/BCGdPg….webp",
    streamServer = "rtmp://alpha8.ingest.gjirafa.tech/live",
    streamKey = "8C31CD90-6DC2-4E38-9FAD-219875271E0D",
    playbackUrl = "https://cdn.vpplayer.tech/aaaaaaaa/M_RL…/index.m3u8",
    liveStatus = OFFLINE,
    healthStatus = UNKNOWN,
    inputSource = null,
)
