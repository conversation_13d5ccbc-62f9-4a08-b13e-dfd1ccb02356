package hero.gcloud

import com.google.cloud.pubsub.v1.Publisher
import com.google.protobuf.ByteString
import com.google.pubsub.v1.PubsubMessage
import com.google.pubsub.v1.TopicName
import hero.baseutils.log
import hero.baseutils.retryOn
import hero.baseutils.truncate
import hero.jackson.map
import hero.jackson.toJson
import java.net.InetAddress
import kotlin.reflect.full.superclasses

class PubSub(
    private val environment: String,
    private val projectId: String,
) {
    private val podName = InetAddress.getLocalHost().hostName
    private val publishedHeader = "X-Published-By"
    private val publishers: MutableMap<String, Publisher> = mutableMapOf()

    // pubsubs allows us to keep only single publisher per topic during applicaton lifetime
    private fun publisherOf(topicNameId: String) =
        publishers.getOrPut(topicNameId) {
            Publisher.newBuilder(TopicName.of(projectId, topicNameId)).build()
        }

    fun <T : Any> publish(value: T) {
        val topicNameId = inferTopicName(value)
        val serializedValue = value.toJson()
        val serializedBytes = serializedValue.toByteArray()
        log.notice("Publishing to topic $topicNameId.", value.map())

        if (serializedBytes.size >= MAX_MESSAGE_SIZE) {
            error("Message size exceeded $MAX_MESSAGE_SIZE bytes.")
        }

        val payloadStart = serializedValue.truncate(MAX_PAYLOAD_LENGTH)
        try {
            retryOn(InterruptedException::class, retryWaitMillis = RETRY_WAIT) {
                val pubsubMessage = PubsubMessage.newBuilder()
                    .setData(ByteString.copyFrom(serializedBytes))
                    .putAttributes(publishedHeader, podName)
                    .build()

                val messageId = publisherOf(topicNameId).publish(pubsubMessage).get()
                log.debug("Record published to topic $topicNameId with id $messageId.")
            }
        } catch (e: Exception) {
            log.error("Unable to publish record $payloadStart to topic $topicNameId due to: ${e.message}.", cause = e)
            // Specific implementation should be responsible for handling the exception regarding to data integrity level.
            throw e
        }
    }

    internal fun <T : Any> inferTopicName(value: T): String {
        val topicClassRaw = value::class
        // when class is sealed, we take its parent to create its topic name
        val firstSuperClass = topicClassRaw.superclasses.firstOrNull()
        val topicClass = if (firstSuperClass != null && firstSuperClass.isSealed)
            firstSuperClass
        else
            topicClassRaw

        return envPrefix(topicClass.simpleName!!)
    }

    fun envPrefix(name: String): String = "${environment.replace("local", "devel")}-$name"
}

private const val MAX_MESSAGE_SIZE = 20 * 1024 * 1024
private const val MAX_PAYLOAD_LENGTH = 128
private const val RETRY_WAIT = 1000L
