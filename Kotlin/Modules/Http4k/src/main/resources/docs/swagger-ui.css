@charset "UTF-8";
@import "swagger-ui-base.css";
@import "swagger-ui-blue.css";

@font-face {
    font-family: 'Silka';
    src: url('https://herohero.co/public/fonts/Silka-BoldItalic.woff2') format('woff2'),
    url('https://herohero.co/public/fonts/Silka-BoldItalic.woff') format('woff');
    font-weight: bold;
    font-style: italic;
    font-display: auto;
}

@font-face {
    font-family: 'Silka';
    src: url('https://herohero.co/public/fonts/Silka-BlackItalic.woff2') format('woff2'),
    url('https://herohero.co/public/fonts/Silka-BlackItalic.woff') format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: auto;
}

@font-face {
    font-family: 'Silka';
    src: url('https://herohero.co/public/fonts/Silka-MediumItalic.woff2') format('woff2'),
    url('https://herohero.co/public/fonts/Silka-MediumItalic.woff') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: auto;
}

@font-face {
    font-family: 'Silka';
    src: url('https://herohero.co/public/fonts/Silka-RegularItalic.woff2') format('woff2'),
    url('https://herohero.co/public/fonts/Silka-RegularItalic.woff') format('woff');
    font-weight: normal;
    font-style: italic;
    font-display: auto;
}

@font-face {
    font-family: 'Silka';
    src: url('https://herohero.co/public/fonts/Silka-Black.woff2') format('woff2'),
    url('https://herohero.co/public/fonts/Silka-Black.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: auto;
}

@font-face {
    font-family: 'Silka';
    src: url('https://herohero.co/public/fonts/Silka-SemiBoldItalic.woff2') format('woff2'),
    url('https://herohero.co/public/fonts/Silka-SemiBoldItalic.woff') format('woff');
    font-weight: 600;
    font-style: italic;
    font-display: auto;
}

@font-face {
    font-family: 'Silka';
    src: url('https://herohero.co/public/fonts/Silka-Bold.woff2') format('woff2'),
    url('https://herohero.co/public/fonts/Silka-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: auto;
}

@font-face {
    font-family: 'Silka';
    src: url('https://herohero.co/public/fonts/Silka-Medium.woff2') format('woff2'),
    url('https://herohero.co/public/fonts/Silka-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: auto;
}

@font-face {
    font-family: 'Silka';
    src: url('https://herohero.co/public/fonts/Silka-Regular.woff2') format('woff2'),
    url('https://herohero.co/public/fonts/Silka-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: auto;
}

@font-face {
    font-family: 'Silka';
    src: url('https://herohero.co/public/fonts/Silka-SemiBold.woff2') format('woff2'),
    url('https://herohero.co/public/fonts/Silka-SemiBold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: auto;
}

body {
    background: #fff !important;
}

h1, h2, h3, h4 {
    font-weight: 600;
}

.swagger-ui .svg-assets {
    display: none;
}

.swagger-ui .info {
    margin: 60px 0 0 0;
}

.swagger-ui .info hgroup.main {
    margin-bottom: 10px;
}

.swagger-ui .topbar {
    display: none;
}

.swagger-ui .opblock-tag {
    border: 0 !important;
}

.swagger-ui .scheme-container {
    padding: 0 0 20px 0 !important;
    margin: 0 !important;
    box-shadow: none;
}

.swagger-ui .servers-title {
    display: none;
}

.swagger-ui .opblock-tag-section {
    padding-top: 40px !important;
    margin: 0;
}

.swagger-ui .information-container .main a {
    display: none;
}

.swagger-ui select {
    font-weight: normal;
    box-shadow: none;
    border-radius: 0;
    background-color: #fff;
    border: 1px solid #DADFE1;
}

.swagger-ui .opblock {
    border-radius: 0;
    box-shadow: none;
}

.swagger-ui .opblock.opblock-get {
    background: none;
}

.swagger-ui .opblock .opblock-summary-operation-id {
    display: none;
}

.swagger-ui section.models {
    margin: 50px 0 !important;
}
