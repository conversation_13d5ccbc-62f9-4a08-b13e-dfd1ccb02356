<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO">
    <Appenders>
        <Console name="ConsoleOut" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %C{3}:%L - %msg\t%throwable%n"/>
        </Console>

        <Console name="ConsoleFluentD" target="SYSTEM_ERR">
            <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="DENY"/>
        </Console>

        <Routing name="Router">
            <Routes pattern="$${env:LOG_APPENDER:-ConsoleOut}">
                <Route ref="ConsoleOut" key="ConsoleOut"/>
                <Route ref="ConsoleFluentD" key="ConsoleFluentD"/>
            </Routes>
        </Routing>
    </Appenders>

    <Loggers>
        <Logger name="jetty.server" level="info" additivity="false"><AppenderRef ref="ConsoleFluentD"/></Logger>
        <Logger name="server.session" level="info" additivity="false"><AppenderRef ref="ConsoleFluentD"/></Logger>
        <Logger name="hero" level="debug" additivity="false"><AppenderRef ref="Router"/></Logger>
        <Root level="info"><AppenderRef ref="Router"/></Root>
    </Loggers>
</Configuration>
