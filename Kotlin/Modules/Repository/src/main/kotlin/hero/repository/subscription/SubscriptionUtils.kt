package hero.repository.subscription

import hero.sql.jooq.Tables.SUBSCRIPTION
import org.jooq.DSLContext

fun canMessage(
    context: DSLContext,
    userId: String,
    targetUserId: String,
): <PERSON><PERSON>an {
    if (userId == targetUserId) {
        return true
    }

    val isSubscribedTo = SUBSCRIPTION.USER_ID.eq(userId).and(SUBSCRIPTION.CREATOR_ID.eq(targetUserId))
    val isSubscribedBy = SUBSCRIPTION.USER_ID.eq(targetUserId).and(SUBSCRIPTION.CREATOR_ID.eq(userId))
    val subscriptionCondition = isSubscribedBy.or(isSubscribedTo)
    val subscription = context
        .select(SUBSCRIPTION.STRIPE_ID)
        .from(SUBSCRIPTION)
        .where(subscriptionCondition)
        .and(JooqSubscriptionHelper.activeSubscription)
        .fetch()

    if (subscription.isNotEmpty) {
        return true
    }

    val mutualCreators = context
        .select(SUBSCRIPTION.CREATOR_ID)
        .from(SUBSCRIPTION)
        .where(SUBSCRIPTION.USER_ID.eq(userId))
        .and(JooqSubscriptionHelper.activeSubscription)
        .intersect(
            context
                .select(SUBSCRIPTION.CREATOR_ID)
                .from(SUBSCRIPTION)
                .where(SUBSCRIPTION.USER_ID.eq(targetUserId))
                .and(JooqSubscriptionHelper.activeSubscription),
        )
        .fetch()

    return mutualCreators.isNotEmpty
}
