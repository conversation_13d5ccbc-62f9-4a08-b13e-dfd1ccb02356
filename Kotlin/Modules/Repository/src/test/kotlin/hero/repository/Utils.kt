package hero.repository

import hero.baseutils.md5nice
import hero.baseutils.truncated
import hero.model.Chapter
import hero.model.Creator
import hero.model.Currency
import hero.model.CustomerIds
import hero.model.DiscordMeta
import hero.model.GjirafaLivestreamMeta
import hero.model.ImageAsset
import hero.model.MessageThread
import hero.model.Notification
import hero.model.NotificationType
import hero.model.NotificationsEnabled
import hero.model.Poll
import hero.model.Post
import hero.model.PostAsset
import hero.model.PostCounts
import hero.model.Role
import hero.model.Session
import hero.model.SignInProvider
import hero.model.SpotifyMeta
import hero.model.StorageEntityType
import hero.model.SupportCounts
import hero.model.User
import hero.model.UserCompany
import hero.model.UserStatus
import hero.model.topics.PostState
import java.time.Instant
import java.util.UUID

// copied from integration testing helper
fun user(
    // note that id must be [a-z-]+
    id: String = "test-account-" + UUID.randomUUID().toString().md5nice(),
    currency: Currency = Currency.EUR,
    counts: SupportCounts = SupportCounts(),
    stripeAccountId: String? = null,
    role: Role = Role.USER,
    status: UserStatus = UserStatus.ACTIVE,
    tierId: String = "${currency}05",
    email: String? = "<EMAIL>",
    emailVerified: Boolean = false,
    path: String = id,
    pathChangedAt: Instant = Instant.ofEpochSecond(0),
    companyCountry: String = "CZ",
    language: String = "en",
    permissions: Int = 0,
    isOfAge: Boolean = false,
    featuredLanguages: List<String> = listOf(),
    bio: String = "bio",
    bioEn: String = "bio-en",
    bioHtml: String = "bio-html",
    bioHtmEn: String = "bio-htm-eng",
    firebaseId: String? = "firebaseId",
    facebookId: String? = "facebookId",
    googleId: String? = "googleId",
    airtableId: String? = "airtableId",
    isExplicit: Boolean = false,
    isFeatured: Boolean = false,
    hasLivestream: Boolean = false,
    hasSpotifyExport: Boolean = false,
    hasDrm: Boolean = false,
    hasRssFeed: Boolean = false,
    image: ImageAsset? = null,
    customerIds: CustomerIds = mutableMapOf(),
    discord: DiscordMeta? = null,
    spotify: SpotifyMeta? = null,
    gjirafaLivestream: GjirafaLivestreamMeta? = null,
    notificationsEnabled: NotificationsEnabled = NotificationsEnabled(),
    verifiedAt: Instant? = null,
) = User(
    id = id,
    name = "test-herohero-$currency-${System.currentTimeMillis()}",
    path = path,
    pathChanged = pathChangedAt,
    email = email,
    emailVerified = emailVerified,
    counts = counts,
    language = language,
    moderatorPermissions = permissions,
    discord = discord,
    spotify = spotify,
    gjirafaLivestream = gjirafaLivestream,
    featuredBy = featuredLanguages,
    firebaseId = firebaseId,
    facebookId = facebookId,
    googleId = googleId,
    airTableId = airtableId,
    explicit = isExplicit,
    featured = isFeatured,
    hasLivestreams = hasLivestream,
    hasSpotifyExport = hasSpotifyExport,
    hasDrm = hasDrm,
    hasRssFeed = hasRssFeed,
    bio = bio,
    bioEn = bioEn,
    bioHtml = bioHtml,
    verifiedAt = verifiedAt,
    bioHtmlEn = bioHtmEn,
    company = UserCompany(
        namePublic = "$id project",
        name = "$id company",
        firstName = "owner first name",
        lastName = "owner last name",
        id = id,
        country = companyCountry,
        birthDate = "2001-10-10",
    ),
    image = image,
    role = role,
    status = status,
    created = Instant.now().truncated(),
    isOfAge = isOfAge,
    creator = if (stripeAccountId == null) {
        Creator(tierId = tierId)
    } else {
        Creator(
            tierId = tierId,
            stripeAccountActive = true,
            stripeAccountOnboarded = true,
            stripeAccountId = stripeAccountId,
            suspended = false,
            currency = currency,
        )
    },
    customerIds = customerIds,
    notificationsEnabled = notificationsEnabled,
)

fun post(
    userId: String,
    parentUserId: String? = null,
    parentPostId: String? = null,
    text: String = "Post text",
    textHtml: String = text,
    textDelta: String = text,
    id: String = UUID.randomUUID().toString(),
    parentId: String? = null,
    siblingId: String? = null,
    state: PostState = PostState.PUBLISHED,
    messageThreadId: String? = null,
    assets: List<PostAsset> = listOf(),
    publishedAt: Instant = Instant.now(),
    pinnedAt: Instant? = null,
    price: Long? = null,
    categories: List<String> = listOf(),
    counts: PostCounts = PostCounts(),
    createdAt: Instant = Instant.now().truncated(),
    updatedAt: Instant = Instant.now().truncated(),
    notifiedAt: Instant = Instant.now().truncated(),
    chapters: List<Chapter> = listOf(),
    excludeFromRss: Boolean = false,
    isAgeRestricted: Boolean = false,
    isSponsored: Boolean = false,
    views: Long = 0,
    poll: Poll? = null,
) = Post(
    id = id,
    text = text,
    textHtml = textHtml,
    textDelta = textDelta,
    state = state,
    userId = userId,
    parentId = parentId,
    siblingId = siblingId,
    parentUserId = parentUserId,
    parentPostId = parentPostId,
    published = publishedAt.truncated(),
    pinnedAt = pinnedAt?.truncated(),
    messageThreadId = messageThreadId,
    assets = assets,
    assetIds = assets.mapNotNull { it.gjirafa?.id ?: it.gjirafaLive?.id },
    price = price,
    counts = counts,
    chapters = chapters,
    categories = categories,
    created = createdAt,
    updated = updatedAt,
    notifiedAt = notifiedAt,
    excludeFromRss = excludeFromRss,
    isAgeRestricted = isAgeRestricted,
    isSponsored = isSponsored,
    views = views,
    poll = poll,
)

fun notification(
    userId: String,
    type: NotificationType = NotificationType.NEW_POST,
    actorIds: List<String> = listOf(userId),
    seenAt: Instant? = null,
    checkedAt: Instant? = null,
    createdAt: Instant = Instant.now(),
    objectId: String? = null,
    objectType: StorageEntityType = StorageEntityType.POST,
    id: String = "$userId-${UUID.randomUUID()}",
) = Notification(
    userId = userId,
    type = type,
    actorIds = actorIds,
    seenAt = seenAt?.truncated(),
    checkedAt = checkedAt?.truncated(),
    created = createdAt.truncated(),
    timestamp = createdAt.truncated(),
    objectId = objectId,
    objectType = objectType,
    id = id,
)

fun session(
    userId: String,
    id: UUID = UUID.randomUUID(),
    userAgent: String = "user-agent",
    ipAddress: String = "127.0.0.1",
    signInLocation: String? = null,
    createdAt: Instant = Instant.now(),
    refreshedAt: Instant = Instant.now(),
    revoked: Boolean = false,
    deviceId: String? = null,
    signInProvider: SignInProvider = SignInProvider.PASSWORD,
) = Session(
    id = id.toString(),
    userId = userId,
    userAgent = userAgent,
    ipAddress = ipAddress,
    signInLocation = signInLocation,
    createdAt = createdAt.truncated(),
    refreshedAt = refreshedAt.truncated(),
    revoked = revoked,
    deviceId = deviceId,
    signInProvider = signInProvider,
)

fun messageThread(
    participants: List<String>,
    seens: Map<String, Instant> = mapOf(),
    checks: Map<String, Instant> = mapOf(),
    canMessage: Map<String, Boolean> = mapOf(),
    archivedFor: List<String> = listOf(),
    deletedFor: List<String> = listOf(),
    deletes: Map<String, Instant> = mapOf(),
    createdAt: Instant = Instant.now(),
    emailNotified: Boolean = false,
    posts: Long = 0L,
    lastMessageAt: Instant? = null,
    lastMessageBy: String? = null,
    lastMessageId: String? = null,
) = MessageThread(
    userIds = participants,
    lastMessageAt = lastMessageAt,
    lastMessageBy = lastMessageBy,
    lastMessageId = lastMessageId,
    posts = posts,
    checks = checks,
    seens = seens,
    canMessage = canMessage,
    archivedFor = archivedFor,
    deletedFor = deletedFor,
    activeFor = listOf(),
    deletes = deletes,
    createdAt = createdAt.truncated(),
    emailNotified = emailNotified,
)
