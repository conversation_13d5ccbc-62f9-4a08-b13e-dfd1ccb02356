package hero.repository.message

import hero.repository.RepositoryTest
import hero.repository.messageThread
import hero.repository.post
import hero.repository.user
import hero.sql.jooq.Tables
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class MessageThreadRepositoryIT : RepositoryTest() {
    @Test
    fun `should save message thread to the database`() {
        val underTest = MessageThreadRepository(testContext)
        createUser(user(id = "user-id-1"))
        createUser(user(id = "user-id-2"))
        createPost(post(id = "post-id-1", userId = "user-id-1"))

        val now = Instant.ofEpochSecond(1746285057)
        val messageThread = messageThread(
            participants = listOf("user-id-1", "user-id-2"),
            seens = mapOf(
                "user-id-1" to now.minusSeconds(60),
            ),
            checks = mapOf(
                "user-id-1" to now.minusSeconds(45),
            ),
            archivedFor = listOf("user-id-2"),
            deletedFor = listOf("user-id-2"),
            deletes = mapOf(
                "user-id-2" to now.minusSeconds(5),
            ),
            canMessage = mapOf(
                "user-id-1" to true,
                "user-id-2" to false,
            ),
            emailNotified = true,
            posts = 3,
            lastMessageAt = now,
            lastMessageBy = "user-id-1",
            lastMessageId = "post-id-1",
            createdAt = now,
        )

        underTest.save(messageThread)

        val messageThreadResult = testContext
            .selectFrom(Tables.MESSAGE_THREAD)
            .fetchSingle()

        assertThat(messageThreadResult.id).isEqualTo(messageThread.id)
        assertThat(messageThreadResult.createdAt).isEqualTo(now)
        assertThat(messageThreadResult.lastMessageAt).isEqualTo(messageThread.lastMessageAt)
        assertThat(messageThreadResult.lastMessageBy).isEqualTo(messageThread.lastMessageBy)
        assertThat(messageThreadResult.postsCount).isEqualTo(3)
        assertThat(messageThreadResult.emailNotified).isTrue()

        val participantsResult = testContext
            .selectFrom(Tables.MESSAGE_THREAD_PARTICIPANT)
            .fetch()

        assertThat(participantsResult).hasSize(2)

        with(participantsResult.first { it.userId == "user-id-1" }) {
            assertThat(threadId).isEqualTo(messageThread.id)
            assertThat(userId).isEqualTo("user-id-1")
            assertThat(seenAt).isEqualTo(now.minusSeconds(60))
            assertThat(checkedAt).isEqualTo(now.minusSeconds(45))
            assertThat(archivedAt).isNull()
            assertThat(deletedAt).isNull()
            assertThat(isDeleted).isFalse()
            assertThat(canMessage).isTrue()
        }

        with(participantsResult.first { it.userId == "user-id-2" }) {
            assertThat(threadId).isEqualTo(messageThread.id)
            assertThat(userId).isEqualTo("user-id-2")
            assertThat(seenAt).isNull()
            assertThat(checkedAt).isNull()
            assertThat(archivedAt).isNotNull()
            assertThat(deletedAt).isEqualTo(now.minusSeconds(5))
            assertThat(isDeleted).isTrue()
            assertThat(canMessage).isFalse()
        }

        // saving again should work fine
        underTest.save(messageThread)
    }
}
