package hero.repository.subscription

import hero.baseutils.plusDays
import hero.baseutils.truncated
import hero.model.SubscriberStatus
import hero.repository.RepositoryTest
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.SubscriptionRecord
import org.assertj.core.api.Assertions.assertThat
import org.jooq.DSLContext
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID

class SubscriptionUtilsIT : RepositoryTest() {
    @Nested
    inner class CanMessage {
        @Test
        fun `user can message himself`() {
            assertThat(canMessage(testContext, "cestmir", "cestmir")).isTrue
        }

        @Test
        fun `user can message another user if he subscribes him`() {
            testContext.createSubscription(userId = "pablo", creatorId = "cestmir")
            assertThat(canMessage(testContext, "pablo", "cestmir")).isTrue
        }

        @Test
        fun `user can message his subscriber`() {
            testContext.createSubscription(userId = "pablo", creatorId = "cestmir")
            assertThat(canMessage(testContext, "cestmir", "pablo")).isTrue
        }

        @Test
        fun `user can message another user if they subscribe a same creator`() {
            testContext.createSubscription(userId = "pablo", creatorId = "cestmir")
            testContext.createSubscription(userId = "filip", creatorId = "cestmir")
            assertThat(canMessage(testContext, "pablo", "filip")).isTrue
        }

        @Test
        fun `users cannot message a user if they don't have any relation together`() {
            testContext.createSubscription(userId = "pablo", creatorId = "nora")
            testContext.createSubscription(userId = "filip", creatorId = "cestmir")
            assertThat(canMessage(testContext, "pablo", "filip")).isFalse
        }
    }
}

fun DSLContext.createSubscription(
    userId: String,
    creatorId: String,
    id: String = UUID.randomUUID().toString(),
    createdAt: Instant = Instant.now(),
    status: SubscriberStatus = SubscriberStatus.ACTIVE,
) {
    val now = Instant.now().truncated()
    val subscriptionRecord = SubscriptionRecord().apply {
        this.stripeId = id
        this.userId = userId
        this.creatorId = creatorId
        this.customerId = "customer-id"
        this.startedAt = now
        this.status = status.name.lowercase()
        this.currency = "EUR"
        this.tierId = "EUR05"
        this.priceCents = 500
        this.createdAt = createdAt.truncated()
        this.endsAt = now.plusDays(10)
    }

    insertInto(Tables.SUBSCRIPTION)
        .set(subscriptionRecord)
        .execute()
}
