package hero.spotify.dto

import com.fasterxml.jackson.annotation.JsonProperty

data class AccessTokenResponse(
    @JsonProperty("access_token")
    val accessToken: String,
    @JsonProperty("token_type")
    val tokenType: String,
    @JsonProperty("expires_in")
    val expiresIn: Long,
    val scope: String,
    @JsonProperty("refresh_token")
    val refreshToken: String?,
)

data class RssFeedPostResponse(
    val reason: String?,
    val spotifyUri: String,
    val alreadyTakenUrl: String?,
    val duplicateName: String?,
)

data class GetEntitlements(
    val entitlements: List<String>,
)

data class GetFeedResponse(
    val spotifyUri: String,
    val statusCode: Int,
    val statusDescription: String,
    val feedDownloadUrl: String,
    val validationErrors: List<ValidationError>?,
)

data class ValidationError(
    val message: String,
)

data class RegisterUserResponse(
    @JsonProperty("completion_url")
    val completionUrl: String,
)
