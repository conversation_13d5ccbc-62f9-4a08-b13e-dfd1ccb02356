package hero.stripe.service

import com.stripe.exception.StripeException
import com.stripe.model.Coupon
import com.stripe.model.Price
import com.stripe.model.Subscription
import com.stripe.param.PriceCreateParams
import com.stripe.param.PriceCreateParams.Recurring.Interval
import com.stripe.param.PriceCreateParams.Recurring.Interval.DAY
import com.stripe.param.PriceCreateParams.Recurring.Interval.MONTH
import com.stripe.param.SubscriptionCreateParams
import com.stripe.param.SubscriptionCreateParams.TrialSettings
import com.stripe.param.SubscriptionCreateParams.TrialSettings.EndBehavior
import com.stripe.param.SubscriptionCreateParams.TrialSettings.EndBehavior.MissingPaymentMethod
import com.stripe.param.SubscriptionListParams
import com.stripe.param.SubscriptionSearchParams
import com.stripe.param.SubscriptionUpdateParams
import hero.baseutils.log
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ServerException
import hero.model.CouponMethod.VOUCHER
import hero.model.Currency
import hero.model.PaymentType
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.Tier
import hero.model.User
import hero.model.euCountries
import hero.model.topics.CardCreateType
import java.math.BigDecimal
import java.time.Instant
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import kotlin.random.Random

class StripeSubscriptionService(
    private val clients: StripeClients,
    private val paymentMethodsService: StripePaymentMethodsService,
    private val production: Boolean,
    private val countryToVatMapping: VatMapping,
    private val recurringPeriod: Interval = if (production) MONTH else DAY,
) {
    fun createRecurringPeriod(recurringPeriod: Interval): PriceCreateParams.Recurring =
        PriceCreateParams.Recurring.builder()
            .setInterval(recurringPeriod)
            .setUsageType(PriceCreateParams.Recurring.UsageType.LICENSED)
            .build()

    fun createPrice(
        creator: User,
        tier: Tier,
    ): Price {
        val product = PriceCreateParams.ProductData.builder()
            .setName("${creator.name} / ${tier.currency} ${tier.priceCents / MAX_PERCENT_VALUE} subscription")
            .build()

        val params = PriceCreateParams.builder()
            .setCurrency(tier.currency.name)
            .setUnitAmount(tier.priceCents.toLong())
            .setProductData(product)
            .setRecurring(createRecurringPeriod(recurringPeriod))
            .build()

        return retry {
            clients[tier.currency].prices().create(params)
        }
    }

    fun findByAppleReference(appleReferenceId: String): Subscription? {
        val searchParams = SubscriptionSearchParams.builder()
            .setQuery("metadata['appleReferenceId']:'$appleReferenceId'")
            .build()
        return listOf(Currency.EUR, Currency.USD)
            .flatMap { clients[it].subscriptions().search(searchParams).data }
            .firstOrNull()
    }

    fun getPrice(
        priceId: String,
        currency: Currency,
    ): Price =
        retry {
            clients[currency].prices().retrieve(priceId)
        }

    fun getSubscriptionsByCustomer(
        customerId: String,
        metaCreatorId: String? = null,
        filterActive: Boolean,
        currency: Currency,
    ): Sequence<Subscription> {
        val params = SubscriptionListParams.builder()
            .setCustomer(customerId)
            //  If no value is supplied, all subscriptions that have not been canceled are returned.
            .setStatus(if (filterActive) null else SubscriptionListParams.Status.ALL)
            .build()

        return retry {
            clients[currency]
                .subscriptions()
                .list(params)
                .autoPagingIterable()
                .asSequence()
                // filter by creator if required
                .let { subs ->
                    if (metaCreatorId == null) subs else subs.filter { it.metadata["creatorId"] == metaCreatorId }
                }
                .let { subs ->
                    if (filterActive)
                        subs.filter { SubscriberStatus.of(it.status).isActive }
                    else
                        subs
                }
        }
    }

    fun getSubscriptionsByCreator(
        creatorId: String,
        filterActive: Boolean,
        currency: Currency,
    ): Sequence<Subscription> {
        val params = SubscriptionSearchParams.builder()
            .setQuery("metadata['creatorId']:'$creatorId'")
            .build()

        return retry {
            clients[currency]
                .subscriptions()
                .search(params)
                .autoPagingIterable()
                .asSequence()
                .let { subs ->
                    if (filterActive)
                        subs.filter { SubscriberStatus.of(it.status).isActive }
                    else
                        subs
                }
        }
    }

    // Invoicing & VAT specification: https://linear.app/herohero/issue/HH-638/invoicing-spec
    fun createSubscription(
        customerId: String,
        paymentMethodId: String?,
        couponId: String?,
        tier: Tier,
        priceId: String,
        creatorId: String,
        userId: String,
        creatorStripeAccountId: String?,
        // we need this nullable/changable only because of tests
        // this can probably be improved and refactored
        onBehalfOf: String? = creatorStripeAccountId,
        creatorCountry: String,
        creatorVatId: String?,
        subscribed: Instant,
        isResubscription: Boolean,
        cardCreateType: CardCreateType?,
        currency: Currency,
        metadata: Map<String, String?> = emptyMap(),
    ): Subscription {
        if ((paymentMethodId == null && tier.priceCents != 0) && couponId == null) {
            error("Both paymentMethodId and couponId cannot be null.")
        }

        val (transferPercents, feePercents, feeVatPercents) = if (tier.priceCents != 0) {
            computeFee(
                tier.feePercents,
                creatorVatId,
                creatorCountry,
                subscribed,
                countryToVatMapping,
            )
        } else {
            Triple(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO)
        }

        val coupon = couponId?.let { getCoupon(currency, couponId) }

        if (paymentMethodId != null) {
            val paymentMethod = clients[currency].paymentMethods().retrieve(paymentMethodId)
            if (paymentMethod.customer == null) {
                paymentMethodsService.putPaymentMethodViaAttach(
                    paymentMethodId = paymentMethodId,
                    customerId = customerId,
                    cardCreateType = cardCreateType,
                    currency = currency,
                    // https://linear.app/herohero/issue/HH-2342
                    makeDefault = true,
                )
            }
        }

        val now = Instant.now()
        val couponDurationInMonths = coupon?.durationInMonths
        val couponDurationInDays = coupon?.metadata?.get("days")?.toLong()

        if (couponDurationInMonths != null && couponDurationInDays != null) {
            throw ConflictException(
                "Coupon $couponId is inconsistent as duration in both `months` and `days` is set.",
                mapOf("creatorId" to creatorId, "userId" to userId),
            )
        }

        val couponMethod = when {
            coupon?.metadata?.get(Subscriber::couponMethod.name) != null ->
                coupon.metadata[Subscriber::couponMethod.name]
            coupon != null ->
                coupon.couponMethod().name
            else ->
                null
        }

        if (couponDurationInDays != null) {
            when {
                couponMethod == VOUCHER.name ->
                    throw ConflictException(
                        "Daily trial must be an 'invite' coupon (not a 'gift'): $couponId",
                        mapOf("creatorId" to creatorId, "userId" to userId),
                    )
                coupon.percentOff != "0.01".toBigDecimal() ->
                    throw ConflictException(
                        "Daily trial must be a coupon with exactly 0.01% off.",
                        mapOf("creatorId" to creatorId, "userId" to userId),
                    )
                else ->
                    {}
            }
        }

        val couponExpiresAt = when {
            couponDurationInMonths != null ->
                now.atZone(ZoneOffset.UTC)
                    .plusMonths(couponDurationInMonths)
                    .truncatedTo(ChronoUnit.SECONDS)
                    .toInstant()
                    .toString()
            couponDurationInDays != null ->
                now.atZone(ZoneOffset.UTC)
                    .plusDays(couponDurationInDays)
                    .truncatedTo(ChronoUnit.SECONDS)
                    .toInstant()
                    .toString()
            else -> null
        }

        val params = SubscriptionCreateParams
            .builder()
            .setDefaultPaymentMethod(paymentMethodId)
            .setCoupon(couponId)
            .addItem(SubscriptionCreateParams.Item.builder().setPrice(priceId).build())
            .setCustomer(customerId)
            .let {
                if (couponDurationInDays != null) {
                    it.setTrialPeriodDays(couponDurationInDays)
                        .setTrialSettings(
                            TrialSettings.builder()
                                .setEndBehavior(
                                    EndBehavior.builder()
                                        .setMissingPaymentMethod(MissingPaymentMethod.CANCEL)
                                        .build(),
                                )
                                .build(),
                        )
                } else {
                    it
                }
            }
            .setMetadata(
                mapOf(
                    Subscriber::creatorId.name to creatorId,
                    Subscriber::userId.name to userId,
                    Subscriber::tierId.name to tier.id,
                    Subscriber::subscribed.name to subscribed.truncatedTo(ChronoUnit.SECONDS).toString(),
                    Subscriber::resubscribedAt.name to
                        if (isResubscription) now.truncatedTo(ChronoUnit.SECONDS).toString() else null,
                    Subscriber::couponAppliedForMonths.name to couponDurationInMonths?.toString(),
                    Subscriber::couponAppliedForDays.name to couponDurationInDays?.toString(),
                    Subscriber::couponExpiresAt.name to couponExpiresAt,
                    Subscriber::couponExpiresNotifiedAt.name to null,
                    Subscriber::couponMethod.name to couponMethod,
                    Subscriber::couponPercentOff.name to
                        if (couponDurationInDays != null) "100" else coupon?.percentOff?.toInt()?.toString(),
                    Subscriber::couponCampaign.name to coupon?.metadata?.get("campaign"),
                    "type" to PaymentType.SUBSCRIPTION.name.lowercase(),
                    // deprecated, use *Decimal
                    "priceCents" to tier.priceCents.toString(),
                    "price" to tier.priceDecimal.toString(),
                    // deprecated, use *Percents
                    "feeCents" to feePercents.toString(),
                    "feePercents" to feePercents.toString(),
                    // deprecated, use *Percents
                    "feeVatCents" to feeVatPercents.toString(),
                    "feeVatPercents" to feeVatPercents.toString(),
                    // deprecated, use *Percents
                    "transferCents" to transferPercents?.toString(),
                    "transferPercents" to transferPercents?.toString(),
                    "creatorCountry" to creatorCountry,
                    "creatorVatId" to creatorVatId,
                    "appFeeHerohero" to tier.heroheroFeeAbsolute.toString(),
                    "appFeeStripeDynamic" to tier.dynamicStripeFeeAbsolute.toString(),
                    "appFeeStripeFixed" to tier.stripeFeeFixed.toString(),
                    "appFeeTotal" to tier.feeAbsolute.toString(),
                ) + metadata,
            )
            .let {
                if (creatorStripeAccountId != null) {
                    it.setTransferData(
                        // Do not use .setApplicationFee to prevent fee currency conversion because of connected accounts different currencies:
                        // https://stripe.com/docs/connect/currencies#application-fees-for-destination-charges-and-converting-balances
                        // We have to use transferData + onBehalfOf to avoid the conversion and still not be the one marked as acquirer.
                        SubscriptionCreateParams.TransferData.builder()
                            .setDestination(creatorStripeAccountId)
                            // only EU: this way we transfer the only the amount without our fee and so the
                            // fee will stay in original currency
                            .setAmountPercent(transferPercents)
                            .build(),
                    )
                } else {
                    it
                }
            }
            // in non-EU we use application fees instead of transfer percentage for clarity
            // as we don't care about currency conversion of EUR-CZK-EUR
            .setApplicationFeePercent(if (creatorCountry !in euCountries) feePercents else null)
            // For newly created accounts, which will have cardPayments capabilities,
            // we can create the subscriptions with "on behalf of" feature. This will
            // note the creator on bank statements and also is a requirement for charges in United States and Australia.
            // https://linear.app/herohero/issue/HH-560/unable-to-change-the-country-in-stripe-when-connecting-new-account
            // https://linear.app/herohero/issue/HH-446/ux-flow-for-selecting-country-before-selecting-tier
            // WARN: we want to use this only for US + AU, otherwise this leads to conversion of EUR to the destination account currency
            .let {
                if (onBehalfOf != null) {
                    it.setOnBehalfOf(inferOnBehalfOf(tier.currency, creatorCountry, onBehalfOf))
                } else {
                    it
                }
            }
            .build()

        if (coupon != null && production) {
            // https://linear.app/herohero/issue/HH-2450/random-delay-when-applying-coupons
            Thread.sleep(Random.nextLong(1000))
        }

        return createSubscription(currency, params)
    }

    internal fun getCoupon(
        currency: Currency,
        couponId: String,
    ): Coupon =
        retry {
            clients[currency].coupons().retrieve(couponId)
        }

    internal fun createSubscription(
        currency: Currency,
        params: SubscriptionCreateParams,
    ): Subscription =
        retry {
            clients[currency].subscriptions().create(params)
        }

    fun patchSubscriptionState(
        customerId: String,
        metaCreatorId: String,
        cancelAtPeriodEnd: Boolean,
        currency: Currency,
    ): Subscription? =
        patchSubscriptionsState(customerId, metaCreatorId, cancelAtPeriodEnd, currency)
            .firstOrNull()

    fun patchSubscriptionsState(
        customerId: String,
        metaCreatorId: String?,
        cancelAtPeriodEnd: Boolean,
        currency: Currency,
    ): List<Subscription> =
        getSubscriptionsByCustomer(customerId, metaCreatorId, true, currency)
            // we need to convert to list, otherwise the sequence might not need to be processed if not iterated
            .toList()
            // only active subscription can be patched
            .filter { it.status != "canceled" }
            // generally there should be only one
            .map {
                log.info(
                    "Stripe customer handling its subscription to cancelAtPeriodEnd:$cancelAtPeriodEnd",
                    mapOf(
                        "customerId" to it.id,
                        "creatorId" to metaCreatorId,
                        "cancelAtPeriodEnd" to cancelAtPeriodEnd,
                        "userId" to it.metadata["userId"],
                        "creatorId" to it.metadata["creatorId"],
                    ),
                )
                try {
                    retry {
                        val patchParams = SubscriptionUpdateParams.builder()
                            .setCancelAtPeriodEnd(cancelAtPeriodEnd)
                            .build()
                        clients[currency].subscriptions().update(it.id, patchParams)
                        // we need to reload the refreshed subscription
                        clients[currency].subscriptions().retrieve(it.id)
                    }
                } catch (e: StripeException) {
                    throw ServerException(
                        e.message,
                        mapOf(
                            "subscriptionId" to it.id,
                            "customerId" to customerId,
                            "userId" to it.metadata["userId"],
                            "creatorId" to it.metadata["creatorId"],
                        ),
                    )
                }
            }
}
