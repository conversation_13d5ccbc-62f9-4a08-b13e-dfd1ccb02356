package hero.stripe.service

import com.stripe.model.Charge
import com.stripe.model.Invoice
import com.stripe.model.Subscription
import com.stripe.model.Transfer
import com.stripe.param.InvoiceListParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.model.Currency
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.time.Instant

class CancelSubscriptionCommandServiceIT {
    private val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
    )

    private val service = spyk(CancelSubscriptionCommandService(mockk()))
    private val currency = Currency.EUR

    // In case this subscription disappears, create a new one in the Testing sandbox dashboard
    // and manually assign connected account acct_1Rc4u0BMU3Qxb6lo. Make sure an Invoice is created
    // with transfer to that very account.
    private val subscription = Subscription().also {
        it.id = "sub_1RvYXJBSudpIq7Tp6qTnNXjs"
        it.metadata = mapOf("userId" to "user", "creatorId" to "creator")
    }

    @Test
    fun `do not refund if the transaction was paid out`() {
        val invoice = Invoice().also {
            it.paid = true
            it.created = Instant.now().epochSecond
            it.currency = currency.name.lowercase()
            it.chargeObject = Charge().also {
                it.id = "ch_123456"
                it.transferObject = Transfer().also {
                    it.reversed = false
                    it.destinationPaymentObject = Charge().also {
                        it.id = "py_123456"
                        it.metadata = mapOf("payoutId" to "po_123456789")
                    }
                }
            }
            it.paymentIntent = "pi_123456"
        }
        every { service.refund(currency, any(), any(), subscription) } just runs
        service.refundInvoiceIfNotPaidOut(currency, subscription, invoice)
        verify(exactly = 0) { service.refund(currency, any(), any(), subscription) }
    }

    @Test
    fun `refund if the transaction was not paid out`() {
        val invoice = Invoice().also {
            it.paid = true
            it.created = Instant.now().epochSecond
            it.currency = currency.name.lowercase()
            it.chargeObject = Charge().also {
                it.id = "ch_123456"
                it.transferObject = Transfer().also {
                    it.reversed = false
                    it.destinationPaymentObject = Charge().also {
                        it.id = "py_123456"
                        it.metadata = mapOf()
                    }
                }
            }
            it.paymentIntent = "pi_123456"
        }
        every { service.refund(currency, any(), any(), subscription) } just runs
        service.refundInvoiceIfNotPaidOut(currency, subscription, invoice)
        verify(exactly = 1) { service.refund(currency, true, "pi_123456", subscription) }
    }

    @Test
    fun `do not refund if the transaction was not paid out, but is old`() {
        val invoice = Invoice().also {
            it.paid = true
            it.created = Instant.now().minusDays(32).epochSecond
            it.currency = currency.name.lowercase()
            it.chargeObject = Charge().also {
                it.id = "ch_123456"
                it.metadata = mapOf()
                it.transferObject = Transfer().also {
                    it.reversed = false
                    it.destinationPaymentObject = Charge().also {
                        it.id = "py_123456"
                        it.metadata = mapOf()
                    }
                }
            }
            it.paymentIntent = "pi_123456"
        }
        every { service.refund(currency, any(), any(), subscription) } just runs
        service.refundInvoiceIfNotPaidOut(currency, subscription, invoice)
        verify(exactly = 0) { service.refund(currency, any(), any(), subscription) }
    }

    @Test
    fun `do refund if a real sandbox transaction was not paid out`() {
        // let's find a latest invoice which we expect not to be paid out yet
        val invoice = stripeClients[Currency.EUR].invoices()
            .list(
                InvoiceListParams.builder()
                    .setStatus(InvoiceListParams.Status.PAID)
                    .setSubscription(subscription.id)
                    .addAllExpand(listOf("data.charge", "data.charge.transfer.destination_payment"))
                    .setLimit(1).build(),
            )
            .autoPagingIterable()
            .first()

        log.info("Testing refund on invoice ${invoice.id}.")
        every { service.refund(currency, any(), any(), subscription) } just runs
        every { service.destinationPaymentOf(invoice) } returns Charge()
            .also {
                it.id = "py_123456"
                it.metadata = mapOf()
            }

        service.refundInvoiceIfNotPaidOut(currency, subscription, invoice)
        verify(exactly = 1) { service.refund(currency, true, any(), subscription) }
    }

    @Test
    fun `do not refund if a real sandbox transaction paid out`() {
        val invoice = stripeClients[Currency.EUR].invoices()
            .list(
                InvoiceListParams.builder()
                    .setStatus(InvoiceListParams.Status.PAID)
                    .setSubscription(subscription.id)
                    .addAllExpand(listOf("data.charge", "data.charge.transfer.destination_payment"))
                    .setLimit(1).build(),
            )
            .autoPagingIterable()
            .first()

        every { service.refund(currency, any(), any(), subscription) } just runs
        every { service.destinationPaymentOf(invoice) } returns Charge()
            .also {
                it.id = "py_123456"
                it.metadata = mapOf("payoutId" to "po_123456789")
            }

        service.refundInvoiceIfNotPaidOut(currency, subscription, invoice)
        verify(exactly = 0) { service.refund(currency, any(), any(), subscription) }
    }
}
