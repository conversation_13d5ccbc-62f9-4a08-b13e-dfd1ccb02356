package hero.api.invoice.util

import com.github.kittinunf.fuel.core.extensions.authentication
import com.github.kittinunf.fuel.httpGet
import hero.baseutils.fetch
import hero.baseutils.retryOn
import hero.baseutils.systemEnv
import hero.exceptions.http.NotFoundException
import hero.model.Currency
import java.io.IOException
import java.math.BigDecimal
import java.time.LocalDate

private val flexibeeEurRatesCache: MutableMap<Pair<Currency, LocalDate>, BigDecimal> = mutableMapOf()

fun fetchConversionRate(
    currency: Currency,
    date: LocalDate,
    /** in case the conversion rate is not yet available, try to fetch the previous day */
    recursive: Boolean = true,
): BigDecimal =
    flexibeeEurRatesCache.getOrPut(Pair(currency, date)) {
        retryOn(IOException::class) {
            "https://herohero.flexibee.eu/c/herohero/kurz/(mena='code:$currency'%20and%20platiOdData='$date').json"
                .httpGet()
                .authentication().basic("herohero-api", systemEnv("FLEXIBEE_PASSWORD"))
                .fetch<FlexibeeKurzResponse>()
                .winstrom
                .kurz
                .firstOrNull()
                ?.nbStred
                ?: if (recursive) {
                    fetchConversionRate(currency, date.minusDays(1), false)
                } else {
                    throw NotFoundException("Cannot find conversion rate for $currency on $date")
                }
        }
    }

private data class FlexibeeKurzResponse(
    val winstrom: FlexibeeKurzWinstrom,
)

private data class FlexibeeKurzWinstrom(
    val kurz: List<FlexibeeKurz>,
)

private data class FlexibeeKurz(
    // cannot be LocalDate as it contains timezone, e.g.: 2025-08-18+02:00
    val platiOdData: String,
    // e.g.: code:EUR
    val mena: String,
    val nbStred: BigDecimal,
)
