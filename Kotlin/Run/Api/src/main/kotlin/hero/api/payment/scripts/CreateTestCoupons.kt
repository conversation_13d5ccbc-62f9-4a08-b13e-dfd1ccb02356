package hero.api.payment.scripts

import hero.baseutils.plusDays
import hero.baseutils.randomString
import hero.gcloud.typedCollectionOf
import hero.model.Currency
import hero.model.Tier
import hero.model.User
import hero.stripe.model.StripePrice
import java.time.Instant

fun main() {
    val isProduction = false
    val creatorId = "rumaanuceblmiy"
    val percentOff = 30
    val couponsToGenerate = 30
    val redemptions = 1
    val months = 9
    val currency = Currency.EUR
    val days = null
    val campaign = "Campaign name"
    val redeemBy = Instant.now().plusDays(30)

    val (firestore, _, _, service, clients) = initializeStripeScript(isProduction)
    val usersCollection = firestore.typedCollectionOf(User)

    val creator = usersCollection[creatorId].get()
    val stripePricesCollection = firestore.typedCollectionOf(StripePrice)
    val stripePriceId = stripePricesCollection["${creator.id}|${creator.creator.tierId}"].get().stripeId

    fun generateCouponId(): String {
        while (true) {
            val couponId = randomString(10).uppercase()
            // check if coupon with given id exists (in both Stripe accounts) and return its value if not
            service.getCouponOrNull(couponId, Currency.USD)
                ?: service.getCouponOrNull(couponId, Currency.EUR)
                ?: return couponId
        }
    }

    (1..couponsToGenerate).toList()
        .parallelStream()
        .forEach {
            val couponId = generateCouponId()
            val price = clients[currency].prices().retrieve(stripePriceId)
            val tier = Tier.ofId(creator.creator.tierId)
            val coupon = service.createCoupon(
                purchasedByUserId = creatorId,
                couponId = couponId,
                creatorId = creator.id,
                tier = tier,
                price = price,
                percentOff = percentOff,
                months = months,
                days = days,
                redeemBy = redeemBy,
                redemptions = redemptions,
                campaign = campaign,
                currency = tier.currency,
            )
            println(coupon.id)
        }
}
