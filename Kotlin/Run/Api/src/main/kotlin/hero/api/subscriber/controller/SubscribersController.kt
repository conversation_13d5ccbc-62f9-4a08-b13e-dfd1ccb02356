package hero.api.subscriber.controller

import hero.api.subscriber.controller.dto.examplePagedSubscriptionResponse
import hero.api.subscriber.controller.dto.exampleSubscriptionResponse
import hero.api.subscriber.controller.dto.toResponse
import hero.api.subscriber.repository.SubscribersSort
import hero.api.subscriber.service.GenerateSubscriberReport
import hero.api.subscriber.service.GetSubscribers
import hero.api.subscriber.service.GetSubscription
import hero.api.subscriber.service.GetSubscriptions
import hero.api.subscriber.service.SubscriberQueryService
import hero.api.subscriber.service.SubscriberReportService
import hero.core.data.PageRequest
import hero.core.data.Sort
import hero.core.data.toResponse
import hero.http4k.auth.getJwtUser
import hero.http4k.controller.QueryUtils
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.body
import hero.http4k.extensions.enum
import hero.http4k.extensions.get
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.boolean

class SubscribersController(
    private val subscriberQueryService: SubscriberQueryService,
    private val subscriberReportService: SubscriberReportService,
) {
    @Suppress("unused")
    val routeGetSubscriptions: ContractRoute =
        ("/v3/users" / Path.userId().of("userId") / "subscriptions").get(
            summary = "List subscriptions for given user.",
            tag = "Subscriptions",
            parameters = paging,
            responses = listOf(Status.OK to examplePagedSubscriptionResponse),
            handler = { request, parameters, userId, _ ->
                val requester = request.getJwtUser()

                val result = subscriberQueryService.execute(
                    GetSubscriptions(
                        userId,
                        requester.id,
                        parameters.expired(request),
                        parameters.pageRequest(request),
                    ),
                )

                val response = result.toResponse {
                    it.toResponse(it.user.id == requester.id || it.creator.id == requester.id)
                }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("unused")
    val routeGetSubscribers: ContractRoute =
        ("/v3/users" / Path.userId().of("userId") / "subscribers").get(
            summary = "List subscribers for given creator.",
            tag = "Subscriptions",
            parameters = paging,
            responses = listOf(Status.OK to examplePagedSubscriptionResponse),
            handler = { request, parameters, creatorId, _ ->
                val requester = request.getJwtUser()

                val result = subscriberQueryService.execute(
                    GetSubscribers(
                        creatorId,
                        requester.id,
                        parameters.expired(request),
                        parameters.pageRequest(request),
                    ),
                )

                val response = result.toResponse {
                    it.toResponse(it.user.id == requester.id || it.creator.id == requester.id)
                }

                Response(Status.OK).body(response)
            },
        )

    @Suppress("unused")
    val routeGetSubscription: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "subscriptions" / Path.userId().of("creatorId")).get(
            summary = "Fetch active subscription of the authenticated user for the specified creator.",
            tag = "Subscriptions",
            parameters = object {},
            responses = listOf(Status.OK to exampleSubscriptionResponse),
            handler = { request, _, userId, _, creatorId ->
                val user = request.getJwtUser()
                require(userId == user.id)

                val query = GetSubscription(userId = user.id, creatorId = creatorId)
                val subscription = subscriberQueryService.execute(query)

                Response(Status.OK).body(subscription.toResponse(true))
            },
        )

    @Suppress("unused")
    val routeGetSubscriber: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "subscribers" / Path.userId().of("subscriberId")).get(
            summary = "Fetch active subscription, of the specified user, for the authenticated user",
            tag = "Subscriptions",
            parameters = object {},
            responses = listOf(Status.OK to exampleSubscriptionResponse),
            handler = { request, _, userId, _, subscriberId ->
                val creator = request.getJwtUser()
                require(userId == creator.id)

                val query = GetSubscription(userId = subscriberId, creatorId = creator.id)
                val subscription = subscriberQueryService.execute(query)

                Response(Status.OK).body(subscription.toResponse(true))
            },
        )

    @Suppress("unused")
    val routeGetSubscribersReport: ContractRoute =
        ("/v1/subscribers/reports").get(
            summary = "Generate subscribers report.",
            tag = "Subscriptions",
            parameters = object {},
            responses = listOf(Status.OK to Unit),
            handler = { request, _ ->
                val user = request.getJwtUser()

                val link = subscriberReportService.execute(GenerateSubscriberReport(user.id))

                Response(Status.FOUND).header("Location", link)
            },
        )
}

private val paging = object {
    val pageSize = QueryUtils.pageSize()
    val afterCursor = QueryUtils.afterCursor()
    val sorting = sorting()

    val expired = Query.boolean().defaulted(
        "expired",
        false,
        "List expired subscriptions instead of active ones. Defaults to `false`.",
    )

    fun pageRequest(request: Request): PageRequest {
        val sorting = sorting(request)
        val pageSize = pageSize(request)
        val afterCursor = afterCursor(request)

        return PageRequest(pageSize = pageSize, afterCursor = afterCursor, sort = Sort.by(sorting))
    }
}

private val subscriberSorts = SubscribersSort.entries.map { it.name.lowercase() }
private val newest = SubscribersSort.NEWEST.name.lowercase()

private fun sorting() =
    Query.enum<SubscribersSort>()
        .defaulted(
            "sorting",
            SubscribersSort.NEWEST,
            "Defines sorting of subscribers. Possible values: $subscriberSorts, default is $newest.",
        )
