package hero.api.invoice.util

import hero.exceptions.http.NotFoundException
import hero.model.Currency
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue

class ConversionRatesUtilsTest {
    @Test
    fun `should fetch conversion rate for today`() {
        val rateEur = fetchConversionRate(Currency.EUR, LocalDate.now())
        val rateUsd = fetchConversionRate(Currency.USD, LocalDate.now())
        assertTrue { rateEur > "20".toBigDecimal() }
        assertTrue { rateUsd > "20".toBigDecimal() }
        assertNotEquals(rateEur, rateUsd)
    }

    @Test
    fun `should fail to fetch conversion rate for future`() {
        assertThrows<NotFoundException> {
            fetchConversionRate(Currency.EUR, LocalDate.now().plusDays(2))
        }
        assertThrows<NotFoundException> {
            fetchConversionRate(Currency.USD, LocalDate.now().plusDays(2))
        }
    }
}
