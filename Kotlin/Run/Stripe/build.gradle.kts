plugins {
    id("hero.kotlin-run-service-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:Http4k"))
    implementation(projectModule(":Modules:Stripe"))
    implementation(projectModule(":Modules:GoogleCloud"))
    implementation(projectModule(":Modules:Jwt"))
    implementation(projectModule(":Modules:Model"))
    implementation(projectModule(":Modules:SQL"))
    implementation(projectModule(":Modules:Exceptions"))

    implementation("com.jsoizo:kotlin-csv-jvm:_")

    testImplementation(projectModule(":Modules:IntegrationTesting"))
}
