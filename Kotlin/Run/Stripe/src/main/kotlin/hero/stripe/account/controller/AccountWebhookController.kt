package hero.stripe.account.controller

import hero.baseutils.log
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.jackson.toJson
import hero.model.Currency
import hero.stripe.account.service.AccountCommandService
import hero.stripe.account.service.ProcessAccount
import hero.stripe.common.currencyQuery
import hero.stripe.common.validatePayload
import hero.stripe.model.StripeAccountWebhook
import hero.stripe.model.StripeWebhookSecrets
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class AccountWebhookController(
    private val accountCommandService: AccountCommandService,
    private val webhookSecrets: Map<Currency, StripeWebhookSecrets>,
) {
    @Suppress("unused")
    val routeStripeWebhookAccounts: ContractRoute =
        "/v1/webhooks/accounts".post(
            summary = "Handle Stripe account events.",
            tag = "Stripe webhooks",
            parameters = object {},
            receiving = null,
            responses = listOf(Status.NO_CONTENT example Unit),
            hideFromOpenApi = true,
            handler = { request, _ ->
                val currency = currencyQuery(request)
                validatePayload(request, webhookSecrets[currency]!!.accounts)
                val body = lens<StripeAccountWebhook>(request)

                log.info("Stripe webhooks us for accounts: ${body.toJson()}")
                accountCommandService.execute(
                    ProcessAccount(
                        accountId = body.account!!,
                        currency = currency,
                    ),
                )

                Response(Status.NO_CONTENT)
            },
        )
}
