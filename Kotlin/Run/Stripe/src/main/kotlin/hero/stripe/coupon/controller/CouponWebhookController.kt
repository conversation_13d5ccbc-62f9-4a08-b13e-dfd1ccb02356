package hero.stripe.coupon.controller

import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.jackson.toJson
import hero.model.Currency
import hero.stripe.common.currencyQuery
import hero.stripe.common.validatePayload
import hero.stripe.coupon.service.CouponCommandService
import hero.stripe.coupon.service.ProcessCoupon
import hero.stripe.model.StripeEventDataObject
import hero.stripe.model.StripeEventRequest
import hero.stripe.model.StripeWebhookSecrets
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class CouponWebhookController(
    private val couponCommandService: CouponCommandService,
    private val webhookSecrets: Map<Currency, StripeWebhookSecrets>,
) {
    @Suppress("unused")
    val routeStripeWebhookCoupons: ContractRoute =
        "/v1/webhooks/coupons".post(
            summary = "Handle Stripe coupons events.",
            tag = "Stripe webhooks",
            parameters = object {},
            receiving = null,
            responses = listOf(Status.NO_CONTENT example Unit),
            hideFromOpenApi = true,
            handler = { request, _ ->
                val currency = currencyQuery(request)
                validatePayload(request, webhookSecrets[currency]!!.coupons)
                val body = lens<StripeEventRequest<StripeEventDataObject>>(request)
                log.info("Stripe webhooks us for coupons: ${body.toJson()}")
                val payload = body.eventData?.payload
                    ?: throw BadRequestException("Stripe event payload was null.")
                if (payload.objectType != "coupon") {
                    throw BadRequestException("Stripe event was not for `coupon`: ${payload.objectType}")
                }
                val couponId = payload.objectId
                    ?: throw BadRequestException("Field objectId was not given.", mapOf())
                log.info("Coupon $couponId state was changed: ${payload.status}")

                couponCommandService.execute(
                    ProcessCoupon(
                        couponId,
                        currency,
                    ),
                )
                Response(Status.NO_CONTENT)
            },
        )
}
