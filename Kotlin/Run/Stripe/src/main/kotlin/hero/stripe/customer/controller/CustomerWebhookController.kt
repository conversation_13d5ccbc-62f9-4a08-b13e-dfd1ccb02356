package hero.stripe.customer.controller

import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.jackson.toJson
import hero.model.Currency
import hero.stripe.common.currencyQuery
import hero.stripe.common.validatePayload
import hero.stripe.customer.service.CustomerCommandService
import hero.stripe.customer.service.ProcessCustomer
import hero.stripe.model.StripeEventDataObject
import hero.stripe.model.StripeEventRequest
import hero.stripe.model.StripeWebhookSecrets
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class CustomerWebhookController(
    private val customerCommandService: CustomerCommandService,
    private val webhookSecrets: Map<Currency, StripeWebhookSecrets>,
) {
    @Suppress("unused")
    val routeStripeCustomer: ContractRoute =
        "/v1/webhooks/customers".post(
            summary = "Handle Stripe customer events.",
            tag = "Stripe webhooks",
            parameters = object {},
            receiving = currencyQuery,
            responses = listOf(
                Status.NO_CONTENT example Unit,
            ),
            hideFromOpenApi = true,
            handler = { request, _ ->
                val currency = currencyQuery(request)
                validatePayload(request, webhookSecrets[currency]!!.customers)
                val body = lens<StripeEventRequest<StripeEventDataObject>>(request)
                log.info("Stripe webhooks us for customer: ${body.toJson()}")
                val payload = body.eventData?.payload ?: throw BadRequestException("Stripe event payload was null.")
                if (payload.objectType != "customer") {
                    throw BadRequestException("Stripe event was not for `customer`: ${payload.objectType}")
                }
                val customerId = payload.objectId ?: throw BadRequestException("Field objectId was not given.")
                log.info("Customer $customerId state was changed: ${payload.status}")
                customerCommandService.execute(
                    ProcessCustomer(
                        customerId,
                        currency,
                    ),
                )
                Response(Status.NO_CONTENT)
            },
        )
}
