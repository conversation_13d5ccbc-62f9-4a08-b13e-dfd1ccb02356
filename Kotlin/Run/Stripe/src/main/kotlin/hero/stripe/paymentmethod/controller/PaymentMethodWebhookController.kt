package hero.stripe.paymentmethod.controller

import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.jackson.toJson
import hero.model.Currency
import hero.stripe.common.currencyQuery
import hero.stripe.common.validatePayload
import hero.stripe.model.StripeEventDataObject
import hero.stripe.model.StripeEventRequest
import hero.stripe.model.StripeWebhookSecrets
import hero.stripe.paymentmethod.service.PaymentMethodCommandService
import hero.stripe.paymentmethod.service.ProcessPaymentMethod
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class PaymentMethodWebhookController(
    private val webhookSecrets: Map<Currency, StripeWebhookSecrets>,
    private val paymentMethodCommandService: PaymentMethodCommandService,
) {
    @Suppress("unused")
    val routeStripeWebhookPaymentMethods: ContractRoute =
        "/v1/webhooks/payment-methods".post(
            summary = "Handle Stripe payment methods creation events.",
            tag = "Stripe webhooks",
            parameters = object {},
            receiving = null,
            responses = listOf(Status.NO_CONTENT example Unit),
            hideFromOpenApi = true,
            handler = { request, _ ->
                val currency = currencyQuery(request)
                validatePayload(request, webhookSecrets[currency]!!.paymentMethods)
                val body = lens<StripeEventRequest<StripeEventDataObject>>(request)
                log.info("Stripe webhooks us for a new payment method: ${body.toJson()}")
                val payload = body.eventData?.payload
                    ?: throw BadRequestException("Stripe event payload was null.")
                if (payload.objectType != "payment_method") {
                    throw BadRequestException("Stripe event was not for `payment_method`: ${payload.objectType}")
                }
                val paymentMethodId = payload.objectId
                    ?: throw BadRequestException("Field objectId was not given.", mapOf())
                val customerId = payload.customerId
                    ?: throw BadRequestException("Field customerId was not given.", mapOf())

                paymentMethodCommandService.execute(
                    ProcessPaymentMethod(
                        customerId = customerId,
                        currency = currency,
                        paymentMethodId = paymentMethodId,
                    ),
                )
                Response(Status.NO_CONTENT)
            },
        )
}
