package hero.stripe.payout.scripts

import com.google.cloud.firestore.Query
import com.stripe.exception.StripeException
import com.stripe.model.BalanceTransaction
import com.stripe.model.Charge
import com.stripe.model.Refund
import com.stripe.param.BalanceTransactionListParams
import hero.baseutils.SystemEnv
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Currency
import hero.model.Invoice
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeService
import hero.stripe.service.stripeAccountRequestOptions
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)
    val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
    )
    val stripe = StripeService(
        clients = stripeClients,
        pubSub = null,
    )

    fun listPayoutTransactions(
        sourceAccountId: String,
        payoutId: String,
        currency: Currency,
    ): Iterable<BalanceTransaction> =
        stripeClients[currency].balanceTransactions().list(
            BalanceTransactionListParams.builder()
                .setLimit(100)
                .addAllExpand(
                    listOf(
                        "data.source.charge",
                    ),
                )
                .setPayout(payoutId)
                .build(),
            sourceAccountId
                .stripeAccountRequestOptions(),
        ).autoPagingIterable()

    val invoicesCollection = firestore.typedCollectionOf(Invoice)

    val start = "202301"
    val end = "202304"
    val executor = Executors.newFixedThreadPool(25)
    invoicesCollection
        .where(Invoice::invoiceId).isGreaterThan(start)
        .orderBy(Invoice::invoiceId, Query.Direction.ASCENDING)
        .fetchAll()
        .filter { it.invoiceId < end }
        .parallelStream()
        .forEach {
            try {
                val trxs = listPayoutTransactions(it.stripeAccountId, it.stripePayoutId, it.currencyInvoice)
                trxs
                    .filter { "payout" !in it.reportingCategory }
                    .filter {
                        val src = it.sourceObject!!
                        when (src) {
                            is Refund -> src.metadata.isEmpty()
                            is Charge -> src.metadata.isEmpty()
                            else -> false
                        }
                    }
                    .forEach { trx ->
                        executor.submit {
                            println(
                                "${it.invoiceId}/${it.stripeAccountId}/${trx.id}/${trx.source} -> ${it.stripePayoutId}",
                            )
                            stripe.markTransactionAsPaidOut(
                                trx,
                                it.stripeAccountId,
                                it.currencyInvoice,
                                it.stripePayoutId,
                            )
                        }
                    }
            } catch (e: StripeException) {
                println("${it.invoiceId}/${it.stripeAccountId}/${e.message}")
            }
        }

    executor.shutdown()
    executor.awaitTermination(Long.MAX_VALUE, TimeUnit.HOURS)
}
