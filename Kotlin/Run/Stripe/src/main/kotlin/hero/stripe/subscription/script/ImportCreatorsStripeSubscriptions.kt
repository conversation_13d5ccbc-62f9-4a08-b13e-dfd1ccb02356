package hero.stripe.subscription.script

import com.stripe.StripeClient
import com.stripe.param.InvoiceSearchParams
import com.stripe.param.SubscriptionSearchParams
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.get
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import hero.stripe.model.StripeKeys
import org.jooq.DSLContext

fun main() {
    val isProduction = true
    val firestore = firestore(SystemEnv.cloudProject, isProduction)
    val stripeKeysRepository = TypedCollectionReference<StripeKeys>(firestore.firestore["constants"])
    val stripeKeysEu = stripeKeysRepository["${isProduction.envPrefix}-stripe-eu"].get()
    val stripeClient = StripeClient(SystemEnv.stripeKeyEu)
    val context = JooqSQL.context(ConnectorConnectionPool.dataSource)

    importSubscriptions("wyornbnmoaiag", stripeClient, context)
}

private fun importSubscriptions(
    creatorId: String,
    stripeClient: StripeClient,
    context: DSLContext,
) {
    stripeClient
        .subscriptions()
        .search(
            SubscriptionSearchParams.Builder()
                .setLimit(100)
                .setQuery("metadata['creatorId']:'$creatorId'")
                .build(),
        )
        .autoPagingIterable()
        .forEach {
            val invoices = stripeClient
                .invoices()
                .search(
                    InvoiceSearchParams.Builder()
                        .setLimit(100)
                        .setQuery("subscription:'${it.id}'")
                        .build(),
                )
                .autoPagingIterable()
                .toList()

            if (invoices.size > 1) {
                println()
            }

            invoices
                .sortedBy { invoice ->
                    invoice.created
                }
                .firstOrNull()?.discount
                ?.run {
                    println("subscription id: ${it.id}, coupon id: ${this.coupon.id}")

                    context.update(Tables.SUBSCRIPTION)
                        .set(Tables.SUBSCRIPTION.COUPON_ID, this.id)
                        .execute()
                }
        }
}
