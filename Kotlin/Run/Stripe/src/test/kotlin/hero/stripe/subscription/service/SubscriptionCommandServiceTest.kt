package hero.stripe.subscription.service

import com.stripe.model.Subscription
import hero.baseutils.instantOf
import org.junit.jupiter.api.Test

class SubscriptionCommandServiceTest {
    @Test
    fun testFlattenSingleCancelledPastDue() {
        val old = Subscription().also {
            it.id = "old"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.currentPeriodEnd = instantOf("2020-01-01T00:00:00Z").epochSecond
            it.status = "cancelled"
            it.canceledAt = Long.MAX_VALUE
        }
        val new = Subscription().also {
            it.id = "new"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.currentPeriodEnd = instantOf("2020-01-01T00:00:00Z").epochSecond
            it.status = "past_due"
        }
        val newest = Subscription().also {
            it.id = "new"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.currentPeriodEnd = instantOf("2020-02-01T00:00:00Z").epochSecond
            it.status = "cancelled"
            it.canceledAt = Long.MAX_VALUE
        }
        val flattened = flattenSubscriptions(sequenceOf(old, new, newest))
        kotlin.test.assertEquals(1, flattened.size)
        kotlin.test.assertEquals(
            "new",
            flattened.values.first().id,
        )
    }

    @Test
    fun testFlattenSingleCancelledActive() {
        val old = Subscription().also {
            it.id = "old"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.currentPeriodEnd = instantOf("2020-01-01T00:00:00Z").epochSecond
            it.status = "cancelled"
            it.canceledAt = Long.MAX_VALUE
        }
        val new = Subscription().also {
            it.id = "new"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.currentPeriodEnd = instantOf("2020-01-01T00:00:00Z").epochSecond
            it.status = "active"
        }
        val flattened = flattenSubscriptions(sequenceOf(old, new))
        kotlin.test.assertEquals(1, flattened.size)
        kotlin.test.assertEquals(
            "new",
            flattened.values.first().id,
        )
    }

    @Test
    fun testFlattenSingleCancelledCancelled() {
        val old = Subscription().also {
            it.id = "old"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.currentPeriodEnd = instantOf("2020-01-01T00:00:00Z").epochSecond
            it.status = "cancelled"
            it.canceledAt = Long.MAX_VALUE
        }
        val newer = Subscription().also {
            it.id = "newer"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.currentPeriodEnd = instantOf("2020-02-01T00:00:00Z").epochSecond
            it.status = "cancelled"
            it.canceledAt = Long.MAX_VALUE
        }
        val newest = Subscription().also {
            it.id = "newest"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.currentPeriodEnd = instantOf("2020-03-01T00:00:00Z").epochSecond
            it.status = "cancelled"
            it.canceledAt = Long.MAX_VALUE
        }

        val flattened = flattenSubscriptions(sequenceOf(newest, old, newer))
        kotlin.test.assertEquals(1, flattened.size)
        kotlin.test.assertEquals(
            "newest",
            flattened.values.first().id,
        )
    }

    @Test
    fun testFlattenNoTimestamp() {
        val newest = Subscription().also {
            it.id = "newest"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.status = "cancelled"
            it.canceledAt = Long.MAX_VALUE
        }

        val flattened = flattenSubscriptions(sequenceOf(newest))
        kotlin.test.assertEquals(1, flattened.size)
        kotlin.test.assertEquals(
            "newest",
            flattened.values.first().id,
        )
    }

    @Test
    fun testFlattenInactive() {
        val newest = Subscription().also {
            it.id = "newest"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.status = "incomplete"
            it.canceledAt = Long.MAX_VALUE
        }

        val flattened = flattenSubscriptions(sequenceOf(newest))
        kotlin.test.assertEquals(1, flattened.size)
        kotlin.test.assertEquals(
            "newest",
            flattened.values.first().id,
        )
    }

    @Test
    fun testFlattenInactives() {
        val inactive1 = Subscription().also {
            it.id = "inactive1"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.status = "incomplete"
            it.canceledAt = Long.MAX_VALUE
        }
        val inactive2 = Subscription().also {
            it.id = "inactive2"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.status = "cancelled"
            it.canceledAt = Long.MAX_VALUE
        }
        val inactive3 = Subscription().also {
            it.id = "inactive3"
            it.metadata = mapOf("creatorId" to "eli-lec")
            it.status = "unpaid"
            it.canceledAt = Long.MAX_VALUE
        }

        val flattened = flattenSubscriptions(sequenceOf(inactive1, inactive2, inactive3))
        kotlin.test.assertEquals(1, flattened.size)
        kotlin.test.assertEquals(
            "inactive1",
            flattened.values.first().id,
        )
    }
}
