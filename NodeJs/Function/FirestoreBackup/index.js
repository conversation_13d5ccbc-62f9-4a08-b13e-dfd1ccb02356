// Google article: https://firebase.google.com/docs/firestore/solutions/schedule-export
// Actually used implentation https://stackoverflow.com/questions/57512324/
const { Firestore } = require('@google-cloud/firestore')
const firestore = new Firestore()
const client = new Firestore.v1.FirestoreAdminClient()
const bucket = process.env.BACKUP_BUCKET;
const project = process.env.CLOUD_PROJECT;
const databaseName = client.databasePath(project, '(default)')
const collectionsToExclude = [
    'prod-posts',
    'prod-sessions',
    'prod-users',
    'prod-notifications',
];

exports.entryPoint = async (event, context) => {
  const collectionsToBackup = await firestore.listCollections()
    .then(collectionRefs => {
      return collectionRefs
        .map(ref => ref.id)
        .filter(id => !id.includes('devel'))
        .filter(id => !collectionsToExclude.includes(id))
    });

  console.log("Exporting collectionIds: " + JSON.stringify(collectionsToBackup));

  return await client
    .exportDocuments({
      name: databaseName,
      outputUriPrefix: bucket,
      // Note we explicitely define all collectionIds to be able to re-import specific collection.
      // https://stackoverflow.com/questions/66381816/restoring-part-of-firestore-export
      collectionIds: [...collectionsToBackup]
    })
    .then(responses => {
      const response = responses[0]
      console.log(`Operation Name: ${response['name']}`)
      return response
    })
    .catch(err => {
      console.error(err)
    })
}
