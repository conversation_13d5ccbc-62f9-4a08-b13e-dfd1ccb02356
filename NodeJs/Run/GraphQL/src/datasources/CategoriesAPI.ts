import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { CategoryDto, CategoryOrdering } from '../generated/api'
import { CategoryModel } from '../models/user'
import { logger } from '../common/logger'

export class CategoriesAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async postCategory({ userId, categoryName }: { userId: string; categoryName: string }): Promise<CategoryModel> {
        const response = await this.post<CategoryDto>(`/v1/users/${userId}/categories`, {
            body: this.requestBody(categoryName, userId),
        })

        if (!response.id || !response.attributes.slug) {
            logger.error(`Got response ${JSON.stringify(response)} which is missing id or slug`)
            throw Error(`Got invalid response from categories endpoint`)
        }

        return {
            id: response.id,
            name: response.attributes.name,
            slug: response.attributes.slug,
        }
    }

    async deleteCategory({ userId, categoryId }: { userId: string; categoryId: string }) {
        await this.delete<CategoryDto>(`/v1/users/${userId}/categories/${categoryId}`)
    }

    async getCategories({ userId }: { userId: string }): Promise<CategoryModel[]> {
        const response = await this.get<{ categories: CategoryDto[] }>(`/v1/users/${userId}/categories`)

        return response.categories.map((category) => ({
            id: category.id ?? '',
            name: category.attributes.name,
            slug: category.attributes.slug ?? '',
        }))
    }

    async patchCategory({
        categoryId,
        categoryName,
        userId,
    }: {
        categoryId: string
        categoryName: string
        userId: string
    }): Promise<CategoryModel> {
        const response = await this.patch<CategoryDto>(`/v1/users/${userId}/categories/${categoryId}`, {
            body: this.requestBody(categoryName, userId, categoryId),
        })

        if (!response.id || !response.attributes.slug) {
            logger.error(`Got response ${JSON.stringify(response)} which is missing id or slug`)
            throw Error(`Got invalid response from categories endpoint`)
        }

        return {
            id: response.id,
            name: response.attributes.name,
            slug: response.attributes.slug,
        }
    }

    async postCategoriesOrder({ userId, categoryIds }: { userId: string; categoryIds: string[] }) {
        const body: CategoryOrdering = {
            categories: categoryIds.map((el) => ({ id: el, type: 'category' })),
        }

        await this.post<CategoryOrdering>(`/v1/users/${userId}/categories-order`, { body })
    }

    requestBody(categoryName: string, userId: string, categoryId?: string): CategoryDto {
        return {
            id: categoryId,
            type: 'category',
            attributes: {
                name: categoryName,
                postCount: 10,
            },
            relationships: {
                user: {
                    id: userId,
                    type: 'user',
                },
            },
        }
    }
}
