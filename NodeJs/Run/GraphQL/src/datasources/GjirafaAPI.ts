import { AugmentedRequest, DataSourceConfig, RESTDataSource } from '@apollo/datasource-rest'
import { userAgent } from './common-utils'
import { GjirafaAssetQuality } from '../generated/resolvers-types'
import { VideoAssetsModelResponseModel } from '../generated/gjirafa'
import { notEmpty } from '../common/util'
import { z } from 'zod'

export class GjirafaAPI extends RESTDataSource {
    apiKey: string
    projectId: string

    constructor(apiKey: string, projectId: string, config?: DataSourceConfig) {
        super(config)
        this.apiKey = apiKey
        this.projectId = projectId
    }

    async getVideoQualities(assetId: string): Promise<GjirafaAssetQuality[]> {
        const params = new URLSearchParams({
            anyStatus: 'true',
        })
        const response = await this.get<VideoAssetsModelResponseModel>(
            `https://vp-api.gjirafa.tech/api/projects/${this.projectId}/videos/assets/${assetId}`,
            { params }
        )

        const streams = response.result?.streamResponse?.streams
        if (streams) {
            return streams
                .flatMap((stream) => stream.qualityTypes)
                .filter(notEmpty)
                .map((qt) => ({
                    duration: qt.duration ?? 0,
                    quality: qt.qualityType ?? '',
                    size: qt.size ?? 0,
                }))
                .filter((qt) => qt.quality.length > 0 && qt.size > 0)
        }

        return []
    }

    async getPlaybackUrl(channelId: string) {
        const response = await this.get(
            `https://livestream.vp.gjirafa.tech/api/v1/${this.projectId}/channels/${channelId}`
        ).then((resp) => GjirafaChannelDetailsResponse.parse(resp))

        const playbackUrl = response.result.playbackUrl

        return playbackUrl?.length == 0 ? null : playbackUrl
    }

    override willSendRequest(_: string, request: AugmentedRequest) {
        request.headers['User-Agent'] = userAgent()
        request.headers['Api-Key'] = this.apiKey
    }
}

const GjirafaChannelDetailsResponse = z.object({
    result: z.object({
        playbackUrl: z.string().nullable(),
    }),
})
