import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { PaginationModel } from '../models/pagination'
import { PagedSavedPostResponse, SavedPostResponse } from '../generated/api'
import { mapToPost } from './common-mappers'
import { SavedPostModel } from '../models/post'
import { PaginationParams, paginationParamsToQueryParams } from './common-utils'

export class LibraryAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getSavedPosts(
        paginationParams: PaginationParams,
        subscribedCreatorsOnly = false
    ): Promise<{ savedPosts: SavedPostModel[]; pagination: PaginationModel }> {
        const params = paginationParamsToQueryParams(paginationParams)
        params.append('subscribedCreatorsOnly', subscribedCreatorsOnly.toString())

        const response = await this.get<PagedSavedPostResponse>(`/v1/library`, { params })
        const savedPosts = response.content.map((savedPost) => ({
            id: savedPost.id,
            savedAt: savedPost.savedAt,
            post: mapToPost(savedPost.post),
        }))

        return {
            savedPosts,
            pagination: {
                hasNextPage: response.hasNext,
                endCursor: response.afterCursor,
            },
        }
    }

    async addPostToLibrary(postId: string): Promise<SavedPostModel> {
        const response = await this.post<SavedPostResponse>('/v1/library', {
            body: {
                postId,
            },
        })

        return {
            id: response.id,
            savedAt: response.savedAt,
            post: mapToPost(response.post),
        }
    }

    async removePostFromLibrary(postId: string) {
        await this.delete(`/v1/library/${postId}`)
    }
}
