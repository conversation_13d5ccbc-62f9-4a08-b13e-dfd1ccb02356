import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'

export class MediaAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'media', cookies, config)
    }

    async getStartDateUTC(videoId: string) {
        const response = await this.get<{ startDateUTC?: string }>(`/v1/gjirafa/live-streams/${videoId}`)

        return response.startDateUTC ?? null
    }
}
