import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { PaginationModel } from '../models/pagination'
import { FullSubscriptionModel, SubscriptionModel } from '../models/subscription'
import { PaginationParams, paginationParamsToQueryParams } from './common-utils'
import {
    CouponMethod,
    PagedSubscriptionResponse,
    Subscriber,
    SubscriptionPatchBody,
    SubscriptionResponse,
} from '../generated/api'
import { mapSubscriptionStatus, mapTier, mapToUser } from './common-mappers'
import { SubscriptionCouponMethod, SubscriptionFilter, SubscriptionOrderBy } from '../generated/resolvers-types'
import { UserJwtRole } from '../auth'

export class SubscriptionAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getSubscribers(
        creatorId: string,
        orderBy: SubscriptionOrderBy,
        paginationParams: PaginationParams
    ): Promise<{ subscriptions: SubscriptionModel[]; pagination: PaginationModel }> {
        return this.fetchSubscriptions(`/v3/users/${creatorId}/subscribers`, orderBy, paginationParams)
    }

    async getSubscriptions(
        userId: string,
        orderBy: SubscriptionOrderBy,
        paginationParams: PaginationParams,
        filter?: SubscriptionFilter
    ): Promise<{ subscriptions: SubscriptionModel[]; pagination: PaginationModel }> {
        return this.fetchSubscriptions(`/v3/users/${userId}/subscriptions`, orderBy, paginationParams, {
            expired: filter?.expired,
        })
    }

    async getSubscription(userId: string, creatorId: string): Promise<FullSubscriptionModel> {
        const response = await this.get<SubscriptionResponse>(`/v1/users/${userId}/subscriptions/${creatorId}`)
        const subscription = this.mapSubscriptionResponse(response)
        if (subscription.subscriptionModelType !== 'full') {
            throw new Error(
                `Returned subscription does not have full details, subscriber: ${userId}, creator: ${creatorId}`
            )
        }

        return subscription
    }

    async getSubscriber(userId: string, subscriberId: string): Promise<FullSubscriptionModel> {
        const response = await this.get<SubscriptionResponse>(`/v1/users/${userId}/subscribers/${subscriberId}`)
        const subscription = this.mapSubscriptionResponse(response)
        if (subscription.subscriptionModelType !== 'full') {
            throw new Error(
                `Returned subscriber does not have full details, subscriber: ${subscriberId}, creator: ${userId}`
            )
        }
        return subscription
    }

    /**
     * Exposes our internal Subscriber, do not use this method unless you have no other choice
     */
    async deleteSubscription(userId: string, creatorId: string, role: UserJwtRole) {
        const params = new URLSearchParams()
        if (role === 'moderator') {
            params.append('refundMethod', 'REFUND')
        }

        return await this.delete<Subscriber>(`/v1/users/${userId}/subscriptions/${creatorId}`, { params })
    }

    /**
     * Exposes our internal Subscriber, do not use this method unless you have no other choice
     */
    async patchSubscription(userId: string, creatorId: string, body: SubscriptionPatchBody) {
        return await this.patch<Subscriber>(`/v1/users/${userId}/subscriptions/${creatorId}`, { body })
    }

    private async fetchSubscriptions(
        resourcePath: string,
        orderBy: SubscriptionOrderBy,
        paginationParams: PaginationParams,
        filter?: {
            expired?: boolean | null
        }
    ) {
        const params = paginationParamsToQueryParams(paginationParams)
        params.append('sorting', orderBy.toString())
        if (filter?.expired) {
            params.append('expired', 'true')
        }

        const response = await this.get<PagedSubscriptionResponse>(resourcePath, { params })
        const subscriptions = response.content.map((subscription) => this.mapSubscriptionResponse(subscription))

        return {
            subscriptions,
            pagination: {
                hasNextPage: response.hasNext,
                endCursor: response.afterCursor,
            },
        }
    }

    private mapSubscriptionResponse(subscription: SubscriptionResponse): SubscriptionModel {
        if (subscription.details) {
            return {
                id: subscription.id,
                subscribedAt: subscription.subscribedAt,
                status: mapSubscriptionStatus(subscription.details.status),
                cancelAtPeriodEnd: subscription.details.cancelAtPeriodEnd,
                expires: subscription.details.expires,
                couponAppliedForMonths: subscription.details.couponAppliedForMonths,
                couponAppliedForDays: subscription.details.couponAppliedForDays,
                couponExpiresAt: subscription.details.couponExpiresAt,
                couponMethod: this.mapcCouponMethod(subscription.details.couponMethod),
                couponPercentOff: subscription.details.couponPercentOff,
                subscriber: mapToUser(subscription.subscriber),
                creator: mapToUser(subscription.creator),
                tier: subscription.tier ? mapTier(subscription.tier) : undefined,
                subscriptionModelType: 'full',
            }
        } else {
            return {
                id: subscription.id,
                subscribedAt: subscription.subscribedAt,
                subscriber: mapToUser(subscription.subscriber),
                creator: mapToUser(subscription.creator),
                subscriptionModelType: 'limited',
            }
        }
    }

    private mapcCouponMethod(couponMethod: CouponMethod | undefined): SubscriptionCouponMethod | undefined {
        if (!couponMethod) {
            return undefined
        }

        switch (couponMethod) {
            case CouponMethod.TRIAL:
                return SubscriptionCouponMethod.TRIAL
            case CouponMethod.VOUCHER:
                return SubscriptionCouponMethod.VOUCHER
        }
    }
}
