import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { AssetTimestamps } from '../models/user'
import { UpdateAssetUserTimestampRequest, UserStore } from '../generated/api'
import { z } from 'zod'

export class UserMediaStoreAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getMediaStore(userId: string): Promise<AssetTimestamps> {
        const response = await this.get<UserStore>(`/v1/users/${userId}/stores/media`)

        const userMediaStoreAttributes = UserMediaStoreAttributes.parse(response.attributes)

        return removeNullValues(userMediaStoreAttributes.content)
    }

    async updateAssetTimestamp(
        assetId: string,
        timestamp: number | undefined | null,
        postId: string | undefined | null
    ) {
        const body: UpdateAssetUserTimestampRequest = {
            assetId,
            timestamp,
            postId,
        }
        await this.put<UserStore>(`/v1/stores/media`, { body })
    }
}

const UserMediaStoreAttributes = z.object({
    content: z.record(z.number().nullable()),
})

function removeNullValues(record: Record<string, number | null>): Record<string, number> {
    return Object.fromEntries(Object.entries(record).filter(([_, value]) => value !== null)) as Record<string, number>
}
