export function paginationParamsToQueryParams(paginationParams: PaginationParams) {
    return new URLSearchParams({
        ...(paginationParams.first && { pageSize: paginationParams.first.toString() }),
        ...(paginationParams.after && { afterCursor: paginationParams.after }),
        ...(paginationParams.before && { beforeCursor: paginationParams.before }),
        ...(paginationParams.sortDirection && { sort: paginationParams.sortDirection }),
    })
}

export type PaginationParams = {
    first?: number | null
    after?: string | null
    before?: string | null
    sortDirection?: 'ASC' | 'DESC' | null
}

export function userAgent() {
    const moduleHash = process.env.MODULE_HASH ? `/${process.env.MODULE_HASH}` : ''
    return `Hero GraphQL Service${moduleHash} (Node ${process.version})`
}
