/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreatorPostsSortingFields } from './CreatorPostsSortingFields';
import type { Direction } from './Direction';
import type { GetCreatorPostsFilter } from './GetCreatorPostsFilter';
export type SearchPostsRequest = {
    creatorId?: string | null;
    afterCursor?: string | null;
    beforeCursor?: string | null;
    pageSize?: number | null;
    filter?: GetCreatorPostsFilter;
    sortBy?: CreatorPostsSortingFields;
    sortDirection?: Direction;
};

