/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CouponMethod } from './CouponMethod';
import type { SubscriberType } from './SubscriberType';
import type { SubscriptionsDtoStatus } from './SubscriptionsDtoStatus';
export type SubscriptionDtoAttributes = {
    subscribedAt?: string | null;
    status?: SubscriptionsDtoStatus;
    cancelAtPeriodEnd?: boolean | null;
    expires?: string | null;
    type?: SubscriberType;
    couponAppliedForMonths?: number | null;
    couponMethod?: CouponMethod;
    couponPercentOff?: number | null;
    couponExpiresAt?: string | null;
};

