/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ListResponseMeta } from './ListResponseMeta';
import type { SubscriptionDto } from './SubscriptionDto';
import type { SubscriptionsDtoIncluded } from './SubscriptionsDtoIncluded';
export type SubscriptionsDtoResponse = {
    meta: ListResponseMeta;
    subscriptions: Array<SubscriptionDto>;
    included: SubscriptionsDtoIncluded;
};

