/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type QualityTypeModel = {
    id?: string | null;
    audioId?: string | null;
    videoId?: string | null;
    mediaId?: string | null;
    mediaType?: string | null;
    qualityType?: string | null;
    streamUrl?: string | null;
    statusDescription?: string | null;
    duration?: number | null;
    size?: number | null;
    width?: number;
    height?: number;
    bitrate?: number;
    audioCodec?: string | null;
    audioBitrate?: number;
    audioSampleRate?: number;
    progress?: number | null;
};

