/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { StreamModel } from './StreamModel';

export type StreamResponseModel = {
    keyId?: string | null;
    contentId?: string | null;
    progress?: number | null;
    progressTillReadiness?: number | null;
    status?: string | null;
    encodingProcessRemainingTime?: number;
    encodingProcessFinishTime?: string | null;
    streams?: Array<StreamModel> | null;
};

