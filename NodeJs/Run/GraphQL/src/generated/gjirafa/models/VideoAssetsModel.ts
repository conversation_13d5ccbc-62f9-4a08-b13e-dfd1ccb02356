/* generated using openapi-typescript-codegen -- do no edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { StreamResponseModel } from './StreamResponseModel';

export type VideoAssetsModel = {
    filmStrip?: string | null;
    filmStripVtt?: string | null;
    thumbnail?: string | null;
    customThumbnail?: string | null;
    thumbnailSecond?: number | null;
    originalFile?: string | null;
    playBackUrl?: string | null;
    streamResponse?: StreamResponseModel;
};

