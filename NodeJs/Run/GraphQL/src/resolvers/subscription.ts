import {
    SubscribeRequestResolvers,
    SubscriptionType,
    UserSubscriptionDetailsResolvers,
    UserSubscriptionResolvers,
} from '../generated/resolvers-types'

export const userSubscriptionResolvers: UserSubscriptionResolvers = {
    __resolveType: ({ subscriptionModelType }) => {
        switch (subscriptionModelType) {
            case 'full':
                return 'UserSubscriptionDetails'
            case 'limited':
                return 'UserSubscriptionInfo'
        }
    },
}

export const userSubscriptionDetailsResolvers: UserSubscriptionDetailsResolvers = {
    type: ({ tier }) => {
        if (tier?.id == 'EUR00') {
            return SubscriptionType.SUBSCRIBE_REQUEST
        } else {
            // eslint-disable-next-line @typescript-eslint/no-deprecated
            return SubscriptionType.STRIPE
        }
    },
}

export const subscribeRequestResolvers: SubscribeRequestResolvers = {
    user: async ({ userId }, _, { dataSources }) => {
        return dataSources.userAPI.getUser(userId)
    },
}
