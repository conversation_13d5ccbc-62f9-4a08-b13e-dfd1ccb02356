import {
    LivestreamDetailsResolvers,
    SubscribeRequestState,
    SubscriptionStatus,
    UserDetailsResolvers,
    UserResolvers,
} from '../generated/resolvers-types'
import { GraphQLError } from 'graphql/error'
import { getCodeFromExtensions } from './utils'
import { plainTextToMarkdown } from '../common/util'
import { logger } from '../common/logger'
import { SubscriptionAPI } from '../datasources/SubscriptionAPI'

export const userResolvers: UserResolvers = {
    subscription: async ({ id }, _, { dataSources, user }) => {
        if (!user?.id) {
            return null
        }

        if (user.id === id) {
            return null
        }

        const subscription = await getSubscription(dataSources.subscriptionAPI, user.id, id)
        if (!subscription) {
            return null
        }

        if (subscription.status === SubscriptionStatus.INACTIVE) {
            return null
        }

        return subscription
    },

    subscriber: async ({ id }, _, { dataSources, user }) => {
        if (!user?.id) {
            return null
        }

        if (user.id === id) {
            return null
        }

        try {
            return await dataSources.subscriptionAPI.getSubscriber(user.id, id)
        } catch (err) {
            if (err instanceof GraphQLError) {
                const code = getCodeFromExtensions(err.extensions)
                if (code === 404 || code === 403 || code === 401) {
                    return null
                }
            }

            throw err
        }
    },

    analytics: ({ analytics }) => {
        return analytics ? analytics : {}
    },

    bioMarkdown: ({ bio }) => {
        return plainTextToMarkdown(bio)
    },

    spotifyShowId: ({ spotifyShowUri }) => {
        if (!spotifyShowUri) return null

        const showId = /^spotify:show:([a-zA-Z0-9]+)$/.exec(spotifyShowUri)

        if (!showId) {
            logger.error(`Failed to parse ${spotifyShowUri}`)
            return null
        }

        return showId[1]
    },

    subscribeRequestState: async ({ id }, _, { dataSources, user }) => {
        if (!user) {
            return null
        }

        if (user.id === id) {
            return null
        }

        const [subscribeRequest, subscription] = await Promise.all([
            dataSources.subscribeRequestAPI.getSubscribeRequest(id),
            getSubscription(dataSources.subscriptionAPI, user.id, id),
        ]).catch((_: unknown) => {
            return [null, null]
        })

        if (!subscribeRequest) {
            return null
        }

        if (subscription && subscription.tier?.id !== 'EUR00') {
            return null
        }

        if (subscribeRequest.acceptedAt) {
            if (subscription?.status === SubscriptionStatus.INACTIVE) {
                return null
            } else {
                return SubscribeRequestState.ACCEPTED
            }
        } else if (subscribeRequest.declinedAt) {
            return SubscribeRequestState.DECLINED
        } else if (subscribeRequest.deletedAt) {
            return null
        } else {
            return SubscribeRequestState.PENDING
        }
    },
}

export const userDetailsResolvers: UserDetailsResolvers = {
    bioMarkdown: ({ bio }) => {
        return plainTextToMarkdown(bio)
    },
}

export const livestreamDetailsResolvers: LivestreamDetailsResolvers = {
    playbackUrl: async (parent, _, { dataSources }) => {
        return await dataSources.gjirafaAPI.getPlaybackUrl(parent.publicId)
    },
}

async function getSubscription(subscriptionAPI: SubscriptionAPI, userId: string, creatorId: string) {
    try {
        return await subscriptionAPI.getSubscription(userId, creatorId)
    } catch (err) {
        if (err instanceof GraphQLError) {
            const code = getCodeFromExtensions(err.extensions)
            if (code === 404 || code === 403 || code === 401) {
                return null
            }
        }

        throw err
    }
}
