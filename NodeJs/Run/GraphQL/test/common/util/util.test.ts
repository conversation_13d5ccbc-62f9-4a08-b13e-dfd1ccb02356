import { plainTextToMarkdown } from '../../../src/common/util'
import { validateUserData } from '../../../src/resolvers/utils'

describe('function: htmlToMarkdown', () => {
    {
        test('should leave plain text as it is', () => {
            const plainText = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit'

            const converted = plainTextToMarkdown(plainText)

            expect(converted).toEqual(plainText)
        })

        test('should convert url links to markdown', () => {
            const plainText = 'Lorem ipsum\nhttps://seznam.cz/aaa/bbb/ccc/ddd/eee/fff\nahoj'

            const converted = plainTextToMarkdown(plainText)

            expect(converted).toEqual(
                'Lorem ipsum\n[seznam.cz/aaa/bbb/ccc/…/fff](https://seznam.cz/aaa/bbb/ccc/ddd/eee/fff)\nahoj'
            )
        })

        test('should convert url links without protocol to markdown', () => {
            const plainText = 'Lorem ipsum\nseznam.cz/aaa/bbb/ccc/ddd/eee/fff\nahoj'

            const converted = plainTextToMarkdown(plainText)

            expect(converted).toEqual(
                'Lorem ipsum\n[seznam.cz/aaa/bbb/ccc/…/fff](http://seznam.cz/aaa/bbb/ccc/ddd/eee/fff)\nahoj'
            )
        })
    }
})

describe('function: validateUserData', () => {
    test('should throw if cookie has expired', () => {
        expect(() => {
            validateUserData({ cookieExpiration: new Date(1741003452 * 1000) })
        }).toThrow()
    })

    test('should throw if nothing is passed in', () => {
        expect(() => {
            validateUserData(undefined)
        }).toThrow()
    })
})
