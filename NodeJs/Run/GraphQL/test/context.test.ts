import { contextFunction } from '../src/context'
import { Request, Response } from 'express'
import { Environment } from '../src/common/environment'
import { parseAuthJwt } from '../src/auth'
import { asMock } from './util'

jest.mock('../src/auth')
const mockedParseUserId = asMock(parseAuthJwt)

test('should correctly create data sources based on cookies and passed options(environment, server, and cookie parser)', async () => {
    mockedParseUserId.mockImplementation(() => ({ sub: '7KpJ9mXzTQvRd4', ro: 1, exp: 1741003452 }))
    const underTest = contextFunction({
        env: Environment.TEST,
        gjirafa: { apiKey: 'test-api-key', projectId: 'test-project-id' },
        internalApiKey: 'internal-api-key',
    })
    const expectedDataSource = {
        environment: Environment.TEST,
        cookies: 'token=7KpJ9mXzTQvRd4',
        serviceName: 'api',
        baseURL: 'https://test-api-90568653510.europe-west1.run.app',
    }

    const result = await underTest({
        res: {} as Response,
        req: { headers: { cookie: 'token=7KpJ9mXzTQvRd4' } } as Request,
    })

    expect(result.user).toEqual({
        id: '7KpJ9mXzTQvRd4',
        role: 'moderator',
        cookieExpiration: new Date(1741003452 * 1000),
    })

    expect(result.dataSources.libraryAPI).toMatchObject(expectedDataSource)
    expect(result.dataSources.messageThreadAPI).toMatchObject(expectedDataSource)
    expect(result.dataSources.notificationAPI).toMatchObject(expectedDataSource)
    expect(result.dataSources.notificationAPI).toMatchObject(expectedDataSource)
    expect(result.dataSources.postAPI).toMatchObject(expectedDataSource)
    expect(result.dataSources.subscriptionAPI).toMatchObject(expectedDataSource)
    expect(result.dataSources.userAPI).toMatchObject(expectedDataSource)
    expect(result.dataSources.statisticsAPI).toMatchObject(expectedDataSource)
    expect(result.dataSources.userMediaStoreAPI).toMatchObject(expectedDataSource)
    expect(result.dataSources.categoriesAPI).toMatchObject(expectedDataSource)
})
