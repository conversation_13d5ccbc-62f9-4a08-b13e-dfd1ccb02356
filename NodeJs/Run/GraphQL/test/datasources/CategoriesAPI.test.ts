import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { CategoriesAPI } from '../../src/datasources/CategoriesAPI'
import { CategoryDto } from '../../src/generated/api'
import { CategoryModel } from '../../src/models/user'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: CategoriesAPI', () => {
    describe('method: postCategory', () => {
        test('should create a request to create new category', async () => {
            // given
            const underTest = new CategoriesAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/users/user-id/categories`, {
                    type: 'category',
                    attributes: {
                        name: 'category',
                        postCount: 10,
                    },
                    relationships: {
                        user: {
                            id: 'user-id',
                            type: 'user',
                        },
                    },
                })
                .reply(200, categoryDto)

            // when
            const category = await underTest.postCategory({ userId: 'user-id', categoryName: 'category' })

            // then
            expect(category).toEqual<CategoryModel>({
                id: 'user-id-category-name',
                slug: 'category-slug',
                name: 'category-name',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: deleteCategory', () => {
        test('should create a request to create new category', async () => {
            // given
            const underTest = new CategoriesAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .delete(`/v1/users/user-id/categories/category-id`)
                .reply(200)

            // when
            await underTest.deleteCategory({ userId: 'user-id', categoryId: 'category-id' })

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: patchCategory', () => {
        test('should create a request to update an existing category', async () => {
            // given
            const underTest = new CategoriesAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .patch(`/v1/users/user-id/categories/category-id`, {
                    id: 'category-id',
                    type: 'category',
                    attributes: {
                        name: 'category',
                        postCount: 10,
                    },
                    relationships: {
                        user: {
                            id: 'user-id',
                            type: 'user',
                        },
                    },
                })
                .reply(200, categoryDto)

            // when
            const category = await underTest.patchCategory({
                userId: 'user-id',
                categoryName: 'category',
                categoryId: 'category-id',
            })

            // then
            expect(category).toEqual<CategoryModel>({
                id: 'user-id-category-name',
                slug: 'category-slug',
                name: 'category-name',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: categoriesOrder', () => {
        test('should create a request to update an existing category', async () => {
            // given
            const underTest = new CategoriesAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .post(`/v1/users/user-id/categories-order`, {
                    categories: [
                        {
                            id: 'one',
                            type: 'category',
                        },
                        {
                            id: 'two',
                            type: 'category',
                        },
                    ],
                })
                .reply(200, categoryDto)

            // when
            await underTest.postCategoriesOrder({
                categoryIds: ['one', 'two'],
                userId: 'user-id',
            })

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getCategories', () => {
        test('should get users categories', async () => {
            // given
            const underTest = new CategoriesAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/users/user-id/categories`)
                .reply(200, { categories: [categoryDto] })

            // when
            const category = await underTest.getCategories({
                userId: 'user-id',
            })

            // then
            expect(category).toEqual<CategoryModel[]>([
                {
                    id: 'user-id-category-name',
                    slug: 'category-slug',
                    name: 'category-name',
                },
            ])
            expect(scope.isDone()).toBeTruthy()
        })
    })
})

const categoryDto: CategoryDto = {
    type: 'category',
    id: 'user-id-category-name',
    attributes: {
        name: 'category-name',
        postCount: 10,
        slug: 'category-slug',
    },
    relationships: {
        user: {
            id: 'user-id',
            type: 'user',
        },
    },
}
