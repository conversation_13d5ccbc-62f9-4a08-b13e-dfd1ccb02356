import nock from 'nock'
import * as GjirafaGetAssetsResponse from './responses/gjirafa-api-get-assets-response.json'
import { GjirafaAPI } from '../../src/datasources/GjirafaAPI'
import { GjirafaAssetQuality } from '../../src/generated/resolvers-types'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: GjirafaAPI', () => {
    test('should fetch gjirafa assets and map video streams', async () => {
        const underTest = new GjirafaAPI('gjirafa-api-key', 'agmipobm')
        const scope = nock('https://vp-api.gjirafa.tech')
            .get(`/api/projects/agmipobm/videos/assets/vjsnpcvc`)
            .query({ anyStatus: true })
            .matchHeader('Api-Key', 'gjirafa-api-key')
            .reply(200, GjirafaGetAssetsResponse)

        // when
        const qualities = await underTest.getVideoQualities('vjsnpcvc')

        // then
        expect(qualities).toEqual<GjirafaAssetQuality[]>([
            { duration: 13.2, quality: '360p', size: 746688 },
            { duration: 13.2, quality: '480p', size: 1474113 },
            { duration: 13.2, quality: '720p', size: 2938391 },
            { duration: 13.2, quality: '1080p', size: 7751210 },
            { duration: 13.2, quality: '1440p', size: 15856078 },
        ])
        expect(scope.isDone()).toBeTruthy()
    })

    test('should fetch channel details', async () => {
        const underTest = new GjirafaAPI('gjirafa-api-key', 'agmipobm')
        const scope = nock('https://livestream.vp.gjirafa.tech')
            .get(`/api/v1/agmipobm/channels/chbddmsbbo`)
            .matchHeader('Api-Key', 'gjirafa-api-key')
            .reply(200, {
                success: true,
                errors: [],
                messages: [],
                result: {
                    publicId: 'chbddmsbbo',
                    name: 'fa4f75bf-c95f-4aa3-8102-6f35f7c45666',
                    title: 'Tereza Tranová',
                    logo: 'https://cdn.vpplayer.tech/agmipobm/g2_97Q0D7cZupS4xt43EqA==,**********/118/channels/chbddmsbbo/logo/b4441190-bc63-4ca7-b769-bb79afdd159f.jpeg',
                    streamServer: 'rtmp://alpha8.ingest.gjirafa.tech/live',
                    streamKey: '493FE733-BCC6-46D7-8517-55C49ADA35D5',
                    playbackUrl:
                        'https://cdn.vpplayer.tech/agmipobm/JWotEFhyKvxdDsvbrEzuXA==,**********/live/493FE733-BCC6-46D7-8517-55C49ADA35D5/index.m3u8',
                    source: 'rtmp://alpha8.ingest.gjirafa.tech/live/493FE733-BCC6-46D7-8517-55C49ADA35D5',
                    liveStatus: 'Offline',
                    healthStatus: 'Unknown',
                    type: 'Standard',
                    typeId: 1,
                    latencyType: 'Normal',
                    latencyTypeId: 1,
                    inputType: 'RtmpPush',
                    filePath: '',
                },
                resultInfo: null,
                statusCode: 200,
            })

        // when
        const qualities = await underTest.getPlaybackUrl('chbddmsbbo')

        // then
        expect(qualities).toEqual(
            'https://cdn.vpplayer.tech/agmipobm/JWotEFhyKvxdDsvbrEzuXA==,**********/live/493FE733-BCC6-46D7-8517-55C49ADA35D5/index.m3u8'
        )
        expect(scope.isDone()).toBeTruthy()
    })
})
