import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { StatisticsAPI } from '../../src/datasources/StatisticsAPI'
import { ExpectedIncomeModel } from '../../src/models/statistics'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: StatisticsAPI', () => {
    describe('method: getExpectedIncome', () => {
        test('should make a call to get expected income', async () => {
            // given
            const underTest = new StatisticsAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/users/creator-id/statistics/income/expected`)
                .reply(200, {
                    grossIncomeCents: 1000,
                    netIncomeCents: 879,
                })

            // when
            const response = await underTest.getExpectedIncome('creator-id')

            // then
            expect(response).toEqual<ExpectedIncomeModel>({
                grossIncomeCents: 1000,
                netIncomeCents: 879,
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })
})
