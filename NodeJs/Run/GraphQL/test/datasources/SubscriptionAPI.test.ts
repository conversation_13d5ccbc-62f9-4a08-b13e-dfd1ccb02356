import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { SubscriptionAPI } from '../../src/datasources/SubscriptionAPI'
import {
    CouponMethod,
    Currency as CurrencyRest,
    PagedSubscriptionResponse,
    SubscriberType,
    SubscriptionResponse,
    SubscriptionsDtoStatus,
} from '../../src/generated/api'
import { PaginationModel } from '../../src/models/pagination'
import { userResponse } from './test-utils'
import { FullSubscriptionModel, LimitedSubscriptionModel, SubscriptionModel } from '../../src/models/subscription'
import {
    Currency,
    SubscriptionCouponMethod,
    SubscriptionOrderBy,
    SubscriptionStatus,
    UserProfileType,
} from '../../src/generated/resolvers-types'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: SubscriptionAPI', () => {
    describe('method: getSubscribers', () => {
        test('should make a call to get creators subscribers with limited access and correctly map it', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v3/users/creator-id/subscribers`)
                .query({ pageSize: 5, afterCursor: 'after-cursor', sorting: 'NEWEST' })
                .reply(200, pagedLimitedSubscriptionResponse())

            // when
            const { subscriptions, pagination } = await underTest.getSubscribers(
                'creator-id',
                SubscriptionOrderBy.NEWEST,
                { first: 5, after: 'after-cursor' }
            )

            // then
            expect(subscriptions).toEqual<SubscriptionModel[]>([{ ...expectedLimitedSubscription }])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: false,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
            })
            expect(scope.isDone()).toBeTruthy()
        })
        test('should make a call to get creators subscribers with full access and correctly map it', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v3/users/creator-id/subscribers`)
                .query({ pageSize: 5, afterCursor: 'after-cursor', sorting: 'NEWEST' })
                .reply(200, pagedFullSubscriptionResponse())

            // when
            const { subscriptions, pagination } = await underTest.getSubscribers(
                'creator-id',
                SubscriptionOrderBy.NEWEST,
                { first: 5, after: 'after-cursor' }
            )

            // then
            expect(subscriptions).toEqual<SubscriptionModel[]>([{ ...expectedFullSubscription }])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: false,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getSubscriptions', () => {
        test('should make a call to get user subscriptions with limited access and correctly map it', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v3/users/user-id/subscriptions`)
                .query({ pageSize: 3, afterCursor: 'after-cursor', sorting: 'NEWEST' })
                .reply(200, pagedLimitedSubscriptionResponse())

            // when
            const { subscriptions, pagination } = await underTest.getSubscriptions(
                'user-id',
                SubscriptionOrderBy.NEWEST,
                { first: 3, after: 'after-cursor' }
            )

            // then
            expect(subscriptions).toEqual<SubscriptionModel[]>([{ ...expectedLimitedSubscription }])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: false,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
            })
            expect(scope.isDone()).toBeTruthy()
        })

        test('should make a call to get user subscriptions with full access and correctly map it', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v3/users/user-id/subscriptions`)
                .query({ pageSize: 3, afterCursor: 'after-cursor', sorting: 'NEWEST' })
                .reply(200, pagedFullSubscriptionResponse())

            // when
            const { subscriptions, pagination } = await underTest.getSubscriptions(
                'user-id',
                SubscriptionOrderBy.NEWEST,
                { first: 3, after: 'after-cursor' }
            )

            // then
            expect(subscriptions).toEqual<SubscriptionModel[]>([{ ...expectedFullSubscription }])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: false,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
            })
            expect(scope.isDone()).toBeTruthy()
        })

        test('should make a call to get user subscriptions with a filter', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v3/users/user-id/subscriptions`)
                .query({ pageSize: 3, afterCursor: 'after-cursor', sorting: 'NEWEST', expired: true })
                .reply(200, pagedFullSubscriptionResponse())

            // when
            const { subscriptions, pagination } = await underTest.getSubscriptions(
                'user-id',
                SubscriptionOrderBy.NEWEST,
                { first: 3, after: 'after-cursor' },
                { expired: true }
            )

            // then
            expect(subscriptions).toEqual<SubscriptionModel[]>([{ ...expectedFullSubscription }])
            expect(pagination).toEqual<PaginationModel>({
                hasNextPage: false,
                endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    test('should make a call to get user subscriptions with full access and correctly map it', async () => {
        // given
        const userId = 'user-id'
        const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
        const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
            .get(`/v3/users/user-id/subscriptions`)
            .query({ pageSize: 3, afterCursor: 'after-cursor', sorting: 'NEWEST' })
            .reply(200, pagedFullSubscriptionResponse())

        // when
        const { subscriptions, pagination } = await underTest.getSubscriptions('user-id', SubscriptionOrderBy.NEWEST, {
            first: 3,
            after: 'after-cursor',
        })

        // then
        expect(subscriptions).toEqual<SubscriptionModel[]>([{ ...expectedFullSubscription }])
        expect(pagination).toEqual<PaginationModel>({
            hasNextPage: false,
            endCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
        })
        expect(scope.isDone()).toBeTruthy()
    })

    describe('method: getSubscription', () => {
        test('should make a call to get user subscription for given creator and correctly map it', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/users/user-id/subscriptions/creator-id`)
                .reply(200, fullSubscriptionResponse())

            // when
            const subscription = await underTest.getSubscription('user-id', 'creator-id')

            // then
            expect(subscription).toEqual<SubscriptionModel>({
                ...expectedFullSubscription,
                subscriptionModelType: 'full',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: getSubscriber', () => {
        test('should make a call to get users specified subscriber and correctly map it', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/users/user-id/subscribers/subscriber-id`)
                .reply(200, fullSubscriptionResponse())

            // when
            const subscription = await underTest.getSubscriber('user-id', 'subscriber-id')

            // then
            expect(subscription).toEqual<SubscriptionModel>({
                ...expectedFullSubscription,
                subscriptionModelType: 'full',
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: deleteSubscription', () => {
        test('should make a call to delete subscription as a user, no mapping done', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .delete(`/v1/users/user-id/subscriptions/creator-id`)
                .query({})
                .reply(200)

            // when
            await underTest.deleteSubscription('user-id', 'creator-id', 'user')

            // then
            expect(scope.isDone()).toBeTruthy()
        })

        test('should make a call to delete subscription as moderator, no mapping done', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .delete(`/v1/users/user-id/subscriptions/creator-id`)
                .query({ refundMethod: 'REFUND' })
                .reply(200)

            // when
            await underTest.deleteSubscription('user-id', 'creator-id', 'moderator')

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })

    describe('method: patchSubscription', () => {
        test('should make a call to patch subscription, no mapping done', async () => {
            // given
            const userId = 'user-id'
            const underTest = new SubscriptionAPI(Environment.DEVEL, userId)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .patch(`/v1/users/user-id/subscriptions/creator-id`, { cancelAtPeriodEnd: true })
                .reply(200)

            // when
            await underTest.patchSubscription('user-id', 'creator-id', { cancelAtPeriodEnd: true })

            // then
            expect(scope.isDone()).toBeTruthy()
        })
    })
})

function pagedFullSubscriptionResponse(): PagedSubscriptionResponse {
    return {
        content: [fullSubscriptionResponse()],
        hasNext: false,
        afterCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
    }
}

function pagedLimitedSubscriptionResponse(): PagedSubscriptionResponse {
    return {
        content: [limitedSubscriptionResponse()],
        hasNext: false,
        afterCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxh',
    }
}

function limitedSubscriptionResponse(): SubscriptionResponse {
    return {
        id: 'subscription-id',
        subscribedAt: '2023-05-11T12:12:11.844280Z',
        creator: userResponse('creator-id'),
        subscriber: userResponse('subscriber-id'),
    }
}

function fullSubscriptionResponse(): SubscriptionResponse {
    return {
        id: 'subscription-id',
        subscribedAt: '2023-05-11T12:12:11.844280Z',
        details: {
            tierId: 'EUR05',
            expires: '2023-05-10T12:12:11.844280Z',
            couponExpiresAt: '2023-10-10T12:12:11.844280Z',
            couponAppliedForMonths: 5,
            couponAppliedForDays: 10,
            couponPercentOff: 50,
            couponMethod: CouponMethod.TRIAL,
            status: SubscriptionsDtoStatus.ACTIVE,
            type: SubscriberType.STRIPE,
            cancelAtPeriodEnd: false,
        },
        creator: userResponse('creator-id'),
        subscriber: userResponse('subscriber-id'),
        tier: {
            id: 'EUR05',
            priceCents: 500,
            currency: CurrencyRest.EUR,
            hidden: false,
            default: false,
        },
    }
}

const expectedFullSubscription: FullSubscriptionModel = {
    id: 'subscription-id',
    expires: '2023-05-10T12:12:11.844280Z',
    couponExpiresAt: '2023-10-10T12:12:11.844280Z',
    couponAppliedForMonths: 5,
    couponAppliedForDays: 10,
    couponPercentOff: 50,
    couponMethod: SubscriptionCouponMethod.TRIAL,
    status: SubscriptionStatus.ACTIVE,
    cancelAtPeriodEnd: false,
    subscribedAt: '2023-05-11T12:12:11.844280Z',
    tier: {
        id: 'EUR05',
        priceCents: 500,
        currency: Currency.EUR,
        hidden: false,
        default: false,
    },
    subscriber: {
        id: 'subscriber-id',
        name: 'user-name',
        bio: 'user-bio',
        image: {
            url: 'image-id',
            width: 420,
            height: 69,
            hidden: true,
        },
        path: 'user-path',
        subscribable: true,
        verified: false,
        counts: {
            supporting: 150,
            supporters: 21231,
            supportersThreshold: 25000,
            posts: 243,
        },
        hasGiftsAllowed: true,
        hasRssFeed: true,
        spotifyShowUri: 'spotify-uri',
        tier: {
            id: 'EUR05',
            priceCents: 500,
            hidden: false,
            default: false,
            currency: Currency.EUR,
        },
        categories: [
            {
                id: 'category-id',
                name: 'category-name',
                slug: 'slug',
            },
        ],
        isDeleted: false,
        privacyPolicyEnabled: false,
        analytics: {
            facebookPixelId: 'facebook-pixel-id',
        },
        emailPublic: '<EMAIL>',
        profileType: UserProfileType.PUBLIC,
    },
    creator: {
        id: 'creator-id',
        name: 'user-name',
        bio: 'user-bio',
        image: {
            url: 'image-id',
            width: 420,
            height: 69,
            hidden: true,
        },
        path: 'user-path',
        subscribable: true,
        verified: false,
        hasGiftsAllowed: true,
        counts: {
            supporting: 150,
            supporters: 21231,
            supportersThreshold: 25000,
            posts: 243,
        },
        hasRssFeed: true,
        spotifyShowUri: 'spotify-uri',
        tier: {
            id: 'EUR05',
            priceCents: 500,
            hidden: false,
            default: false,
            currency: Currency.EUR,
        },
        categories: [
            {
                id: 'category-id',
                name: 'category-name',
                slug: 'slug',
            },
        ],
        isDeleted: false,
        privacyPolicyEnabled: false,
        analytics: {
            facebookPixelId: 'facebook-pixel-id',
        },
        emailPublic: '<EMAIL>',
        profileType: UserProfileType.PUBLIC,
    },
    subscriptionModelType: 'full',
}

const expectedLimitedSubscription: LimitedSubscriptionModel = {
    id: 'subscription-id',
    subscribedAt: '2023-05-11T12:12:11.844280Z',
    subscriber: {
        id: 'subscriber-id',
        name: 'user-name',
        bio: 'user-bio',
        image: {
            url: 'image-id',
            width: 420,
            height: 69,
            hidden: true,
        },
        hasGiftsAllowed: true,
        path: 'user-path',
        subscribable: true,
        verified: false,
        counts: {
            supporting: 150,
            supporters: 21231,
            supportersThreshold: 25000,
            posts: 243,
        },
        hasRssFeed: true,
        spotifyShowUri: 'spotify-uri',
        tier: {
            id: 'EUR05',
            priceCents: 500,
            hidden: false,
            default: false,
            currency: Currency.EUR,
        },
        categories: [
            {
                id: 'category-id',
                name: 'category-name',
                slug: 'slug',
            },
        ],
        isDeleted: false,
        privacyPolicyEnabled: false,
        analytics: {
            facebookPixelId: 'facebook-pixel-id',
        },
        emailPublic: '<EMAIL>',
        profileType: UserProfileType.PUBLIC,
    },
    creator: {
        id: 'creator-id',
        name: 'user-name',
        bio: 'user-bio',
        image: {
            url: 'image-id',
            width: 420,
            height: 69,
            hidden: true,
        },
        hasGiftsAllowed: true,
        path: 'user-path',
        subscribable: true,
        verified: false,
        counts: {
            supporting: 150,
            supporters: 21231,
            supportersThreshold: 25000,
            posts: 243,
        },
        hasRssFeed: true,
        spotifyShowUri: 'spotify-uri',
        tier: {
            id: 'EUR05',
            priceCents: 500,
            hidden: false,
            default: false,
            currency: Currency.EUR,
        },
        categories: [
            {
                id: 'category-id',
                name: 'category-name',
                slug: 'slug',
            },
        ],
        isDeleted: false,
        privacyPolicyEnabled: false,
        analytics: {
            facebookPixelId: 'facebook-pixel-id',
        },
        emailPublic: '<EMAIL>',
        profileType: UserProfileType.PUBLIC,
    },
    subscriptionModelType: 'limited',
}
