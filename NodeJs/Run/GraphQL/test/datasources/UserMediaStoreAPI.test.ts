import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { UserMediaStoreAPI } from '../../src/datasources/UserMediaStoreAPI'
import { AssetTimestamps } from '../../src/models/user'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: UserMediaStoreAPI', () => {
    test('should make a call to fetch users media store and map asset timestamps', async () => {
        // given
        const underTest = new UserMediaStoreAPI(Environment.DEVEL)
        const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
            .get(`/v1/users/user-id/stores/media`)
            .reply(200, {
                id: 'media',
                attributes: {
                    content: {
                        'asset-id': 420,
                    },
                },
                type: 'store',
            })

        // when
        const post = await underTest.getMediaStore('user-id')

        // then
        expect(post).toEqual<AssetTimestamps>({ 'asset-id': 420 })
        expect(scope.isDone()).toBeTruthy()
    })

    test('should make a call update asset timestamp', async () => {
        // given
        const underTest = new UserMediaStoreAPI(Environment.DEVEL)
        const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
            .put(`/v1/stores/media`, {
                assetId: 'vjsntmow',
                timestamp: 300.2,
                postId: 'post-id',
            })
            .reply(200)

        // when
        await underTest.updateAssetTimestamp('vjsntmow', 300.2, 'post-id')

        // then
        expect(scope.isDone()).toBeTruthy()
    })
})
