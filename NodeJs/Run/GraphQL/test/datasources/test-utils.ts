import {
    Currency,
    DocumentType,
    GjirafaStatus,
    LiveVideoStatus,
    PagedPostResponse,
    PollResponse,
    PostResponse,
    PostState as PostDtoState,
    UserResponse,
} from '../../src/generated/api'
import { UserProfileType } from '../../src/generated/resolvers-types'

export function pagedPostResponse(config?: PostResponseConfig): PagedPostResponse {
    return {
        content: [postResponse(config)],
        hasNext: true,
        afterCursor: 'eyJ1c2VySWQiOiJodW5naG9hbmd6Zmd2bWRlbSIsImxhc3RNZXNzYWdlQXQiOiIyMD',
    }
}

export function postResponse(config?: PostResponseConfig): PostResponse {
    return {
        id: 'post-id',
        text: 'post-text',
        textHtml: '<h1>Header</h1>',
        textDelta: '{}',
        publishedAt: '2023-09-17T00:00:00Z',
        pinnedAt: '2023-09-18T00:00:00Z',
        price: 10,
        chapters: [],
        counts: {
            comments: 15,
            replies: 20,
        },
        fullAsset: false,
        relationships: {
            userId: 'user-id',
            messageThreadId: 'message-thread-id',
            parentId: config?.parentId,
        },
        excludeFromRss: false,
        state: PostDtoState.DELETED,
        assets: [
            {
                image: {
                    url: 'image-url',
                    width: 700,
                    height: 600,
                },
            },
            {
                document: {
                    type: DocumentType.DOCX,
                    url: 'document-url',
                    name: 'document-name',
                },
            },
            {
                gjirafa: {
                    hidden: false,
                    hasAudio: true,
                    hasVideo: true,
                    imageKey: 'image-key',
                    key: 'sfyMS3YYBbCjjZhQG4D7Ig==,7991601123',
                    keyId: '20FFB244-27C5-0C40-A00B-D28096FF7216',
                    id: 'id',
                    duration: 45.1,
                    progressTillCompleteness: 0,
                    progressTillReadiness: 0,
                    status: GjirafaStatus.COMPLETE,
                    projectId: 'project-id',
                    previewStaticUrl:
                        'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-thumbnail.webp',
                    previewAnimatedUrl:
                        'https://cdn.vpplayer.tech/agmipobm/tNTT3UXuKWZTxrJpT36D0w==,4837254998/images/vjsnqosr/original-animated.webp',
                    previewStripUrl:
                        'https://cdn.vpplayer.tech/agmipobm/epDKgAZEDoALFiwtjVcmIg==,7991601123/encode/vjsnxsqt/thumbnails/filmstrip.vtt',
                },
                thumbnail: 'thumbnail',
                thumbnailImage: {
                    url: 'thumbnail',
                    width: 700,
                    height: 600,
                },
            },
            {
                gjirafaLive: {
                    id: 'vdjnejr',
                    liveStatus: LiveVideoStatus.LIVE,
                    channelPublicId: 'adqmnej',
                    playbackUrl: 'playback-url',
                },
                thumbnail: 'thumbnail',
                thumbnailImage: {
                    url: 'thumbnail',
                    width: 700,
                    height: 600,
                },
            },
            {
                youTube: { id: 'youtube-id' },
                thumbnail: 'thumbnail-youtube',
                thumbnailImage: {
                    url: 'thumbnail',
                    width: 700,
                    height: 600,
                },
            },
            {},
        ],
        ...(config?.savedPostInfo && {
            savedPostInfo: {
                id: 'saved-post-id',
                savedAt: '2023-09-18T00:00:00Z',
            },
        }),
        categories: [
            {
                id: 'category-id',
                name: 'category-name',
                slug: 'category-slug',
            },
        ],
        isSponsored: true,
        isAgeRestricted: false,
    }
}

type PostResponseConfig = { parentId?: string; savedPostInfo?: boolean }

export function userResponse(userId: string): UserResponse {
    return {
        id: userId,
        name: 'user-name',
        bio: 'user-bio',
        bioEn: 'user-bio-en',
        bioHtmlEn: '<h1>user-bio-en</h1>',
        bioHtml: '<h1>user-bio</h1>',
        image: {
            id: 'image-id',
            width: 420,
            height: 69,
            hidden: true,
        },
        path: 'user-path',
        subscribable: true,
        verified: false,
        counts: {
            supporting: 150,
            supporters: 21231,
            supportersThreshold: 25000,
            posts: 243,
        },
        hasRssFeed: true,
        tier: {
            id: 'EUR05',
            priceCents: 500,
            hidden: false,
            default: false,
            currency: Currency.EUR,
        },
        categories: [
            {
                id: 'category-id',
                name: 'category-name',
                slug: 'slug',
            },
        ],
        isDeleted: false,
        privacyPolicyEnabled: false,
        analytics: {
            facebookPixelId: 'facebook-pixel-id',
        },
        hasGiftsAllowed: true,
        spotify: {
            spotifyUri: 'spotify-uri',
            hasSpotifyConnection: true,
        },
        emailPublic: '<EMAIL>',
        profileType: UserProfileType.PUBLIC,
    }
}

export function pollResponse(): PollResponse {
    return {
        id: 'poll-id',
        deadline: '2024-12-31T23:59:59Z',
        options: [
            {
                id: 'option-1',
                title: 'Option 1',
                voteCount: 10,
                hasVotedFor: false,
            },
            {
                id: 'option-2',
                title: 'Option 2',
                voteCount: 5,
                hasVotedFor: true,
            },
        ],
    }
}
