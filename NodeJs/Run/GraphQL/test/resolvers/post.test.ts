import { comment, post, postAsset, testContext, user } from './test-utils'
import { resolvers } from '../../src/resolvers'
import { GraphQLResolveInfo } from 'graphql/type'
import { GjirafaQualityTypeStatus, PostSortFields, SortDirection } from '../../src/generated/resolvers-types'

describe('post resolvers', () => {
    describe('resolving post type', () => {
        test.each<{
            fullAssets: boolean
            expectedResolveType: string
        }>([
            {
                fullAssets: true,
                expectedResolveType: 'CompleteContentPost',
            },
            {
                fullAssets: false,
                expectedResolveType: 'LimitedContentPost',
            },
        ])('%s', async ({ fullAssets, expectedResolveType }) => {
            const context = testContext()

            const result = await resolvers.Post!.__resolveType(post({ fullAssets }), context, {} as GraphQLResolveInfo)

            expect(result).toEqual(expectedResolveType)
        })
    })

    test('field: user', async () => {
        const context = testContext()
        const expectedUser = user('user-id')
        context.dataSources.userAPI.getUser = jest.fn(async () => expectedUser)

        const result = await resolvers.Post!.user!(post({ userId: 'user-id' }), {}, context, {} as GraphQLResolveInfo)

        expect(context.dataSources.userAPI.getUser).toHaveBeenCalledWith('user-id')
        expect(result).toEqual(expectedUser)
    })

    test('field: posts', async () => {
        const context = testContext()
        const expectedPost = post({ id: 'post-id' })
        const expectedPosts = {
            posts: [expectedPost],
            pagination: {
                hasNextPage: true,
                endCursor: 'end-cursor',
            },
        }
        context.dataSources.postAPI.getPosts = jest.fn(async () => expectedPosts)

        const publishedAt = '2023-05-10T12:12:11.844280Z'
        const result = await resolvers.Post!.posts!(
            post({ userId: 'user-id', publishedAt }),
            { first: 10, direction: SortDirection.DESC },
            context,
            {} as GraphQLResolveInfo
        )

        const expectedCursor = {
            lastPublishedAt: publishedAt,
            '@type': 'GetCreatorPostsPublishedAtCursor',
        }
        const expectedEncodedCursor = Buffer.from(JSON.stringify(expectedCursor)).toString('base64')

        expect(context.dataSources.postAPI.getPosts).toHaveBeenCalledWith(
            'user-id',
            {
                first: 10,
                after: expectedEncodedCursor,
            },
            { by: PostSortFields.PUBLISHED_AT, order: SortDirection.DESC },
            {}
        )
        expect(result).toEqual({
            nodes: [expectedPost],
            pageInfo: {
                hasNextPage: true,
                endCursor: 'end-cursor',
            },
        })
    })

    describe('resolving post assets type', () => {
        test.each<{
            assetType: 'document' | 'image' | 'gjirafa' | 'gjirafa-livestream' | 'empty'
            expectedResolveType: string
        }>([
            {
                assetType: 'document',
                expectedResolveType: 'PostDocumentAsset',
            },
            {
                assetType: 'image',
                expectedResolveType: 'PostImageAsset',
            },
            {
                assetType: 'gjirafa',
                expectedResolveType: 'PostGjirafaAsset',
            },
            {
                assetType: 'gjirafa-livestream',
                expectedResolveType: 'PostGjirafaLivestreamAsset',
            },
            {
                assetType: 'empty',
                expectedResolveType: 'PostEmptyAsset',
            },
        ])('%s', async ({ assetType, expectedResolveType }) => {
            const context = testContext()

            const result = await resolvers.PostAsset!.__resolveType(
                postAsset(assetType),
                context,
                {} as GraphQLResolveInfo
            )

            expect(result).toEqual(expectedResolveType)
        })
    })

    describe('CompleteContentPost resolvers', () => {
        test('field: comments', async () => {
            const context = testContext()
            const expectedComment = comment()
            const expectedComments = {
                comments: [expectedComment],
                pagination: {
                    hasNextPage: true,
                },
            }
            context.dataSources.postAPI.getComments = jest.fn(async () => expectedComments)

            const result = await resolvers.CompleteContentPost!.comments!(
                post({ id: 'post-id' }),
                { first: 10, sortDirection: SortDirection.ASC },
                context,
                {} as GraphQLResolveInfo
            )

            expect(context.dataSources.postAPI.getComments).toHaveBeenCalledWith('post-id', {
                first: 10,
                sortDirection: SortDirection.ASC,
            })
            expect(result).toEqual({
                nodes: [expectedComment],
                pageInfo: {
                    hasNextPage: true,
                },
            })
        })
    })
})
describe('postGjirafaAsset resolvers', () => {
    describe('field: timestamp', () => {
        test('unauthenticated request', async () => {
            const context = testContext()

            const timestamp = await resolvers.PostGjirafaAsset!.timestamp!(
                { ...gjirafaAsset },
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(timestamp).toBeNull()
        })

        test('videoStreamUrl is missing for a video', async () => {
            const context = testContext({ userId: 'user-id' })

            const timestamp = await resolvers.PostGjirafaAsset!.timestamp!(
                { ...gjirafaAsset, hasVideo: true, videoStreamUrl: undefined, audioStreamUrl: 'none-null' },
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(timestamp).toBeNull()
        })

        test('audioStreamUrl is missing for a audio', async () => {
            const context = testContext({ userId: 'user-id' })

            const timestamp = await resolvers.PostGjirafaAsset!.timestamp!(
                { ...gjirafaAsset, hasVideo: false, videoStreamUrl: 'none-null', audioStreamUrl: undefined },
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(timestamp).toBeNull()
        })

        test('should return undefined if media store has no timestamps', async () => {
            const context = testContext({ userId: 'user-id' })
            context.dataSources.userMediaStoreAPI.getMediaStore = jest.fn(async () => ({}))

            const timestamp = await resolvers.PostGjirafaAsset!.timestamp!(
                {
                    ...gjirafaAsset,
                    hasVideo: true,
                    videoStreamUrl:
                        'https://cdn.vpplayer.tech/agmipobm/4645dgLhfeq8iJy8d9J0bw==,7991601123/encode/vjsnnmzr/hls/master_file.m3u8',
                    audioStreamUrl: undefined,
                },
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(timestamp).toBeUndefined()
        })

        test('should return a timestamp for a video', async () => {
            const context = testContext({ userId: 'user-id' })
            context.dataSources.userMediaStoreAPI.getMediaStore = jest.fn(async () => ({
                'https://cdn.vpplayer.tech/agmipobm/4645dgLhfeq8iJy8d9J0bw==,7991601123/encode/vjsnnmzr/hls/master_file.m3u8': 620.1887333460022,
            }))

            const timestamp = await resolvers.PostGjirafaAsset!.timestamp!(
                {
                    ...gjirafaAsset,
                    hasVideo: true,
                    videoStreamUrl:
                        'https://cdn.vpplayer.tech/agmipobm/4645dgLhfeq8iJy8d9J0bw==,7991601123/encode/vjsnnmzr/hls/master_file.m3u8',
                    audioStreamUrl: undefined,
                },
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(timestamp).toEqual(620.1887333460022)
        })

        test('should return a timestamp for an audio', async () => {
            const context = testContext({ userId: 'user-id' })
            context.dataSources.userMediaStoreAPI.getMediaStore = jest.fn(async () => ({
                'https://cdn.vpplayer.tech/agmipobm/qMtrWRJtSnsYwEzOwG7-Jg==,7991601123/audio-encode/bbgjraawn/hls/320kbps/audio/index.m3u8': 2.736885692318971,
            }))

            const timestamp = await resolvers.PostGjirafaAsset!.timestamp!(
                {
                    ...gjirafaAsset,
                    hasVideo: false,
                    videoStreamUrl: undefined,
                    audioStreamUrl:
                        'https://cdn.vpplayer.tech/agmipobm/qMtrWRJtSnsYwEzOwG7-Jg==,7991601123/audio-encode/bbgjraawn/hls/320kbps/audio/index.m3u8',
                },
                {},
                context,
                {} as GraphQLResolveInfo
            )

            expect(timestamp).toEqual(2.736885692318971)
        })
    })
})

const gjirafaAsset = {
    width: 10,
    height: 10,
    keyId: '',
    hasVideo: true,
    hasAudio: true,
    id: 'vjnff',
    gjirafaId: 'vjnff',
    duration: 100,
    previewAnimatedUrl: '',
    previewStaticUrl: '',
    previewStripUrl: '',
    status: GjirafaQualityTypeStatus.COMPLETE,
    hidden: false,
    progressTillReadiness: 1,
    progressTillCompleteness: 1,
    isLivestreamRecording: false,
}
