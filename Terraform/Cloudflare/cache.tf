resource "cloudflare_ruleset" "caching_rules" {
  kind    = "zone"
  name    = "default"
  phase   = "http_request_cache_settings"
  zone_id = "bbef51b8496d6d21c2315c0032e9f54c"

  rules {
    action = "set_cache_settings"
    action_parameters {
      cache = false
    }
    description = "About cache is disabled"
    enabled     = true
    expression  = "(http.host eq \"about.herohero.co\")"
  }

  rules {
    action = "set_cache_settings"
    action_parameters {
      cache = true
      edge_ttl {
        mode = "bypass_by_default"
        status_code_ttl {
          status_code_range {
            from = 400
            to   = 403
          }
          # cache for a month
          value = 2678400
        }
        status_code_ttl {
          status_code = 200
          # cache for an hour
          value = 3600
        }
      }
    }
    description = "Force cache RSS feeds"
    enabled     = true
    expression  = "(http.request.uri.path contains \"/rss-feed/\")"
  }

  # Note we should not cache CSS/JS because it breaks new deployments.
  rules {
    action      = "set_cache_settings"
    description = "Force cache Fonts, Icons"
    enabled     = true
    expression = join(" or ", [
      "(ends_with(http.request.uri.path, \".woff2\"))",
      "(ends_with(http.request.uri.path, \".ico\"))",
      "(starts_with(http.request.uri.path, \"/public/icons/\"))",
      "(ends_with(http.request.uri.path, \"touch-icon.png\"))",
      "(ends_with(http.request.uri.path, \"/site.webmanifest\"))",
    ])
    action_parameters {
      cache = true
      cache_key {
        custom_key {
          query_string {
            exclude = ["*"]
          }
        }
      }
      edge_ttl {
        default = 2678400
        mode    = "override_origin"
      }
    }
  }

  rules {
    action      = "set_cache_settings"
    description = "Cache partner-id-verifier"
    enabled     = true
    expression  = "(http.request.uri.path wildcard \"/partner-id-verifier/*\")"

    action_parameters {
      cache = true

      edge_ttl {
        mode = "respect_origin"
      }
    }
  }
}
