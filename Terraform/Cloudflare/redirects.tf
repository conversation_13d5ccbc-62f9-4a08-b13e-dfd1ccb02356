variable "non_auth_functions_redirects" {
  default = [
    "gift-voucher", "invoice-generator", "invoice-report-generator",
    "product-feed-generator", "rss-feed", "creator-privacy-policy-generator",
  ]
}

resource "cloudflare_ruleset" "redirect_about_based_on_language" {
  kind    = "zone"
  name    = "default"
  phase   = "http_request_dynamic_redirect"
  zone_id = cloudflare_zone.herohero_co.id
  rules {
    action = "redirect"
    action_parameters {
      from_value {
        preserve_query_string = true
        status_code           = 302
        target_url {
          value = "https://about.herohero.co/cs/"
        }
      }
    }
    description = "About to CS"
    enabled     = true
    expression  = "(http.request.full_uri eq \"https://about.herohero.co/\" and http.request.accepted_languages[0] eq \"cs\")"
    ref         = "9b95559917924a838f9aa8cf1fb4bf5a"
  }
  rules {
    action = "redirect"
    action_parameters {
      from_value {
        preserve_query_string = true
        status_code           = 302
        target_url {
          value = "https://about.herohero.co/en/"
        }
      }
    }
    description = "About to ES"
    enabled     = true
    expression  = "(http.request.full_uri eq \"https://about.herohero.co/\" and http.request.accepted_languages[0] eq \"es\")"
  }
  rules {
    action = "redirect"
    action_parameters {
      from_value {
        preserve_query_string = true
        status_code           = 302
        target_url {
          value = "https://about.herohero.co/en/"
        }
      }
    }
    description = "About to DE"
    enabled     = true
    expression  = "(http.request.full_uri eq \"https://about.herohero.co/\" and http.request.accepted_languages[0] eq \"de\")"
  }
  rules {
    action = "redirect"
    action_parameters {
      from_value {
        preserve_query_string = true
        status_code           = 302
        target_url {
          value = "https://about.herohero.co/en/"
        }
      }
    }
    description = "About to EN"
    enabled     = true
    expression  = "(http.request.full_uri eq \"https://about.herohero.co/\" and http.request.accepted_languages[0] ne \"cs\" and http.request.accepted_languages[0] ne \"de\" and http.request.accepted_languages[0] ne \"es\")"
  }
  # TODO remove legacy redirects in 2025/06
  rules {
    action = "redirect"
    action_parameters {
      from_value {
        preserve_query_string = false
        status_code           = 301
        target_url {
          expression = "concat(\"https://svc-prod.herohero.co\", substring(http.request.uri.path, 19), \"/?\", http.request.uri.query)"
        }
      }
    }
    description = "Redirect prod legacy url functions"
    enabled     = true
    expression  = "(http.host eq \"herohero.co\" and http.request.uri.path contains \"/services/functions/\")"
  }
  # TODO remove legacy redirects in 2025/06
  rules {
    action      = "redirect"
    description = "Redirect legacy prod invoices"
    enabled     = true
    expression  = "(http.host eq \"herohero.co\" and http.request.uri.path eq \"/invoice\")"

    action_parameters {
      from_value {
        preserve_query_string = false
        status_code           = 301

        target_url {
          expression = "concat(\"https://svc-prod-na.herohero.co/invoice-generator/?\", http.request.uri.query)"
        }
      }
    }
  }

  # TODO remove legacy redirects in 2025/06
  dynamic "rules" {
    for_each = var.non_auth_functions_redirects
    content {
      action      = "redirect"
      description = "Redirect legacy ${rules.value} to non-auth"
      enabled     = true
      expression  = "(http.host eq \"svc-prod.herohero.co\" and http.request.uri.path eq \"/${rules.value}/\")"
      action_parameters {
        from_value {
          preserve_query_string = false
          status_code           = 301

          target_url {
            expression = "concat(\"https://svc-prod-na.herohero.co/${rules.value}/?\", http.request.uri.query)"
          }
        }
      }
    }
  }

  rules {
    action      = "redirect"
    description = "Redirect legacy tax manual"
    enabled     = true
    expression  = "http.request.full_uri eq \"https://help.herohero.co/guide/for-creators.html\""

    action_parameters {
      from_value {
        preserve_query_string = false
        status_code           = 301

        target_url {
          value = "https://help.herohero.co/cs/articles/9681333-uctovani-a-dane"
        }
      }
    }
  }
}

// remove www from www.herohero.co
resource "cloudflare_page_rule" "page_rule_remove_www" {
  zone_id = cloudflare_zone.herohero_co.id
  target  = "www.herohero.co/*"
  status  = "active"
  lifecycle {
    ignore_changes = [priority]
  }
  priority = 1
  actions {
    forwarding_url {
      status_code = 301
      url         = "https://herohero.co/$1"
    }
  }
}

// terms cs
resource "cloudflare_page_rule" "page_rule_terms_cs" {
  zone_id = cloudflare_zone.herohero_co.id
  target  = "https://static.herohero.co/docs/legal/terms-cs.pdf"
  status  = "active"
  lifecycle {
    ignore_changes = [priority]
  }
  priority = 1
  actions {
    forwarding_url {
      status_code = 301
      url         = "https://static.herohero.co/docs/legal/terms-2025-02-05-cs.pdf"
    }
  }
}

// terms en
resource "cloudflare_page_rule" "page_rule_terms_en" {
  zone_id = cloudflare_zone.herohero_co.id
  target  = "https://static.herohero.co/docs/legal/terms-en.pdf"
  status  = "active"
  lifecycle {
    ignore_changes = [priority]
  }
  priority = 1
  actions {
    forwarding_url {
      status_code = 301
      url         = "https://static.herohero.co/docs/legal/terms-2025-02-05-en.pdf"
    }
  }
}
