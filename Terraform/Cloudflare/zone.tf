resource "cloudflare_zone" "herohero_co" {
  zone       = "herohero.co"
  account_id = "32db2dd53fd139ada1bde55cd5be200e"
  plan       = "pro"
}

resource "cloudflare_zone_settings_override" "partners-zone-settings" {
  zone_id = cloudflare_zone.herohero_co.id
  settings {
    always_online            = "off"
    always_use_https         = "on"
    automatic_https_rewrites = "on"
    brotli                   = "on"
    browser_cache_ttl        = 0
    browser_check            = "on"
    cache_level              = "aggressive"
    challenge_ttl            = 604800
    development_mode         = "off"
    email_obfuscation        = "off"
    hotlink_protection       = "off"
    http3                    = "on"
    ip_geolocation           = "on"
    ipv6                     = "on"
    max_upload               = 100
    min_tls_version          = "1.0"
    opportunistic_encryption = "on"
    opportunistic_onion      = "on"
    privacy_pass             = "on"
    pseudo_ipv4              = "off"
    rocket_loader            = "off"
    security_header {
      enabled            = false
      include_subdomains = false
      max_age            = 0
      nosniff            = false
      preload            = false
    }
    security_level      = "high"
    server_side_exclude = "on"
    ssl                 = "full"
    tls_1_3             = "zrt"
    tls_client_auth     = "off"
    websockets          = "on"
    zero_rtt            = "on"
  }
}
