# note that the service id is misleading because the service is also allowed to write
resource "google_service_account" "pubsub-reader" {
  account_id   = "pubsub-reader"
  display_name = "PubSub Writer and Reader"
}

resource "google_service_account" "gitlab-runner" {
  account_id   = "gitlab-runner"
  display_name = "GitLab Runner"
}

resource "google_service_account" "firestore-backup" {
  account_id   = "firestore-backup"
  display_name = "Firestore backup"
}

resource "google_service_account" "cloud-runner" {
  account_id   = "cloud-run"
  display_name = "Cloud Runner"
}
