# https://console.cloud.google.com/cloudscheduler?project=heroheroco&folder=&organizationId=

resource "google_cloud_scheduler_job" "devel_minutely" {
  name        = "devel-minutely"
  description = "Send a minutely devel cron message."
  # every min
  schedule  = "* * * * *"
  time_zone = "Europe/London"

  retry_config {
    retry_count = 1
  }

  pubsub_target {
    topic_name = "projects/heroheroco/topics/devel-Minutely"
    data       = base64encode("{}")
  }
}

resource "google_cloud_scheduler_job" "devel_schedule_hourly" {
  name        = "devel-hourly"
  description = "Send a hourly devel cron message."
  # every 60 mins
  schedule  = "0 * * * *"
  time_zone = "Europe/London"

  retry_config {
    retry_count = 1
  }

  pubsub_target {
    topic_name = "projects/heroheroco/topics/devel-Hourly"
    data       = base64encode("{}")
  }
}

resource "google_cloud_scheduler_job" "devel_schedule_daily" {
  name        = "devel-daily"
  description = "Send a daily devel cron message."
  # every day
  schedule  = "0 9 * * *"
  time_zone = "Europe/London"

  retry_config {
    retry_count = 1
  }

  pubsub_target {
    topic_name = "projects/heroheroco/topics/devel-Daily"
    data       = base64encode("{}")
  }
}

resource "google_cloud_scheduler_job" "prod_minutely" {
  name        = "prod-minutely"
  description = "Send a minutely production cron message."
  # every min
  schedule  = "* * * * *"
  time_zone = "Europe/London"

  retry_config {
    retry_count = 1
  }

  pubsub_target {
    topic_name = "projects/heroheroco/topics/prod-Minutely"
    data       = base64encode("{}")
  }
}

resource "google_cloud_scheduler_job" "prod_schedule_hourly" {
  name        = "prod-hourly"
  description = "Send a hourly production cron message."
  # every 60 mins
  schedule  = "0 * * * *"
  time_zone = "Europe/London"

  retry_config {
    retry_count = 1
  }

  pubsub_target {
    topic_name = "projects/heroheroco/topics/prod-Hourly"
    data       = base64encode("{}")
  }
}

resource "google_cloud_scheduler_job" "prod_schedule_daily" {
  name        = "prod-daily"
  description = "Send a daily production cron message."
  # every day
  schedule  = "0 9 * * *"
  time_zone = "Europe/London"

  retry_config {
    retry_count = 1
  }

  pubsub_target {
    topic_name = "projects/heroheroco/topics/prod-Daily"
    data       = base64encode("{}")
  }
}
