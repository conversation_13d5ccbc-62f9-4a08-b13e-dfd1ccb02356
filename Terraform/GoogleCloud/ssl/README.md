# Self signed certificates for GCP Load balancer

We use several sub-domains within our Load balancer which are hidden 
behind Cloudflare and therefore these do not need a valid certificate. 
For such cases it is recommended to generate a wildcard self-signed certificate
which will only be used for communication between GCP and Cloudflare.
This certificate can also be expired and it will work correctly.

If you need to generate a new one, just run `./gencert.sh` and confirm 
all default values with `[enter]`.

This should then be used withing `loadbalancer.tf` script.