variable "buckets_to_backup" {
  default = [
    # Terraform does not allow variables in variables, we need to write actual names as described in:
    # https://stackoverflow.com/questions/58841060/terraform-variables-within-variables
    {
      source = "heroheroco-assets"        # google_storage_bucket.heroheroco-assets
      target = "heroheroco-assets-backup" # google_storage_bucket.heroheroco-assets-backup
    },
    {
      source = "heroheroco-terraform-state"        # google_storage_bucket.heroheroco-terraform-state
      target = "heroheroco-terraform-state-backup" # google_storage_bucket.heroheroco-terraform-state_backup
    },
    {
      source = "heroheroco-assets-static"        # google_storage_bucket.heroheroco-assets-static
      target = "heroheroco-assets-static-backup" # google_storage_bucket.heroheroco-assets-static_backup
    },
    # When creating new transfer jobs, Terraform will not assign correct permissions for the transfer
    # service account (<EMAIL>).
    # These must be created manually (https://stackoverflow.com/q/********/) or ideally (TODO) as a Terraform resource.
  ]
}

resource "google_storage_transfer_job" "heroheroco_storage_backup" {
  count       = length(var.buckets_to_backup)
  project     = var.project
  description = join("", [var.buckets_to_backup[count.index].source, " to ", var.buckets_to_backup[count.index].target])
  transfer_spec {
    object_conditions {
      max_time_elapsed_since_last_modification = "86400s"
    }
    transfer_options {
      delete_objects_from_source_after_transfer  = false
      delete_objects_unique_in_sink              = false
      overwrite_objects_already_existing_in_sink = true
    }
    gcs_data_source {
      bucket_name = var.buckets_to_backup[count.index].source
    }
    gcs_data_sink {
      bucket_name = var.buckets_to_backup[count.index].target
    }
  }
  schedule {
    schedule_start_date {
      year  = 1970
      month = 01
      day   = 1
    }
    start_time_of_day {
      hours   = 4
      minutes = 27
      seconds = 0
      nanos   = 42
    }
  }
}
