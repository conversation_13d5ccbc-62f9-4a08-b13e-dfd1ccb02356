resource "google_storage_bucket" "heroheroco_terraform_state" {
  name          = "heroheroco-terraform-state"
  location      = var.region
  storage_class = "STANDARD"

  lifecycle_rule {
    action {
      type = "Delete"
    }

    condition {
      age                        = 0
      days_since_custom_time     = 0
      days_since_noncurrent_time = 0
      num_newer_versions         = 10
      with_state                 = "ARCHIVED"
    }
  }
  lifecycle_rule {
    action {
      type = "Delete"
    }

    condition {
      age                        = 0
      days_since_custom_time     = 0
      days_since_noncurrent_time = 7
      num_newer_versions         = 0
      with_state                 = "ANY"
    }
  }

  versioning {
    enabled = true
  }
}

resource "google_storage_bucket" "heroheroco_terraform_state_backup" {
  name          = "heroheroco-terraform-state-backup"
  location      = var.region
  storage_class = "STANDARD"
  versioning {
    enabled = true
  }
}

resource "google_storage_bucket" "heroheroco_assets" {
  name          = "heroheroco-assets"
  location      = var.region
  storage_class = "STANDARD"
  cors {
    origin          = ["https://herohero.co", "https://prod.herohero.co", "https://staging.herohero.co", "https://preview.herohero.co", "https://devel.herohero.co", "https://local.herohero.co", "https://local.herohero.co:3000"]
    method          = ["POST", "GET", "PUT", "OPTIONS"]
    response_header = ["Authorization", "Content-Length", "Content-Range", "Content-Type", "Access-Control-Allow-Origin"]
    max_age_seconds = 3600
  }
}

resource "google_storage_bucket" "heroheroco_assets_backup" {
  name          = "heroheroco-assets-backup"
  location      = var.region
  storage_class = "STANDARD"
}

resource "google_storage_bucket" "heroheroco_assets_static" {
  name          = "heroheroco-assets-static"
  location      = var.region
  storage_class = "STANDARD"
  cors {
    origin          = []
    method          = []
    response_header = []
    max_age_seconds = 3600
  }
  versioning {
    enabled = true
  }
}

resource "google_storage_bucket" "heroheroco_assets_static_backup" {
  name          = "heroheroco-assets-static-backup"
  location      = var.region
  storage_class = "STANDARD"
  cors {
    origin          = []
    method          = []
    response_header = []
    max_age_seconds = 3600
  }
  versioning {
    enabled = true
  }
}

resource "google_storage_bucket" "heroheroco_gitlab_build_cache" {
  name          = "heroheroco-gitlab-build-cache"
  location      = var.region
  storage_class = "STANDARD"
}

resource "google_storage_bucket" "heroheroco_firestore_backups" {
  name          = "heroheroco-firestore-backup"
  location      = var.region
  storage_class = "ARCHIVE"
}
