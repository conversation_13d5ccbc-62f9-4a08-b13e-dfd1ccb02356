resource "google_compute_network" "vpc" {
  project = var.project
  name    = "herohero"

  auto_create_subnetworks = false

  # depends_on = [time_sleep.wait_for_compute_engine]
}

resource "google_compute_subnetwork" "lb" {
  project       = var.project
  name          = "herohero-lb"
  network       = google_compute_network.vpc.id
  ip_cidr_range = "********/24"
  region        = "europe-west1"
}

resource "google_compute_subnetwork" "google_access" {
  project                  = var.project
  name                     = "herohero-google-access"
  ip_cidr_range            = "********/16"
  region                   = "europe-west1"
  network                  = google_compute_network.vpc.id
  private_ip_google_access = true
}

resource "google_compute_subnetwork" "private" {
  project       = var.project
  name          = "herohero-private"
  network       = google_compute_network.vpc.id
  ip_cidr_range = "**********/16"
  region        = "europe-west1"

  purpose = "PRIVATE"
  role    = "ACTIVE"
}

/*
TODO: paused until someone can figure out VPC connection to SQL

# VPC Connector is used to connect Cloud Run services and functions to a VPC
# network. This allows the service to access resources in the VPC network, such
# as Compute Engine instances, Cloud SQL databases, and Memorystore instances.
# Also, this allows the service to use private endpoints to connect to other
# Cloud Run services and functions in the same VPC network.
# See: https://cloud.google.com/run/docs/securing/private-networking
#
# For more Terraform/OpenTofu examples, check out this repository:
# https://github.com/GoogleCloudPlatform/cloud-foundation-fabric/tree/master/blueprints/serverless
#
# VPC Connector vs Direct VPC Egress:
# Direct VPC Egress is feature available only to Cloud Run services using the v2
# of Cloud Run and is also not available in all regions. VPC Connector is
# available in all regions and can be used with both v1 and v2 of Cloud Run.
# VPC Connector also supports Cloud Functions, which Direct VPC Egress does not.
resource "google_vpc_access_connector" "connector" {
  name    = "hh-vpc-conn"
  region  = "europe-west1"
  network = google_compute_network.vpc.name

  # The allocated IP range must be a CIDR block that does not conflict with any subnets (e.g., "private").
  ip_cidr_range = "********/28"

  # VPC Access Connector is using an autoscaling group and this configures its
  # minimum and maximum number of instances.
  min_instances = 2
  max_instances = 3

  # See: https://cloud.google.com/vpc/pricing#serverless-vpc-pricing
  machine_type = "e2-micro" # f1-micro | e2-micro | e2-standard-4
}

// See: https://cloud.google.com/vpc/docs/configure-private-service-connect-apis
// To use the endpoint, use DNS `p.googleapis.com`.
// For example, if your endpoint name is xyz, DNS records are created for
// storage-xyz.p.googleapis.com, compute-xyz.p.googleapis.com, and other
// commonly used APIs in the API bundle.
resource "google_compute_global_address" "vpc_endpoint" {
  project      = var.project
  name         = "global-psconnect-ip-prod"
  address_type = "INTERNAL"
  purpose      = "PRIVATE_SERVICE_CONNECT"
  network      = google_compute_network.vpc.id
  address      = "********"
}

resource "google_compute_global_forwarding_rule" "vpc_endpoint" {
  project               = var.project
  name                  = "vpcendpoint" // max 23 characters !!!
  target                = "all-apis"
  network               = google_compute_network.vpc.id
  ip_address            = google_compute_global_address.vpc_endpoint.id
  load_balancing_scheme = ""
}
*/